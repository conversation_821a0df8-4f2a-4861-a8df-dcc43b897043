#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單的瀏覽器啟動測試
"""

import os
import sys
import subprocess
import webbrowser

def test_browser_methods():
    """測試各種瀏覽器啟動方法"""
    print("=== 簡單瀏覽器啟動測試 ===")
    
    test_url = "http://localhost:8765/test"
    print(f"測試 URL: {test_url}")
    
    # 方法 1: xdg-open
    print("\n--- 測試 xdg-open ---")
    try:
        result = subprocess.run(['xdg-open', test_url], capture_output=True, timeout=5)
        if result.returncode == 0:
            print("✅ xdg-open 執行成功")
        else:
            print(f"❌ xdg-open 失敗，返回碼: {result.returncode}")
            if result.stderr:
                print(f"錯誤: {result.stderr.decode()}")
    except Exception as e:
        print(f"❌ xdg-open 異常: {e}")
    
    # 方法 2: google-chrome
    print("\n--- 測試 google-chrome ---")
    try:
        cmd = ['google-chrome', '--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu', test_url]
        result = subprocess.run(cmd, capture_output=True, timeout=5)
        if result.returncode == 0:
            print("✅ google-chrome 執行成功")
        else:
            print(f"❌ google-chrome 失敗，返回碼: {result.returncode}")
            if result.stderr:
                print(f"錯誤: {result.stderr.decode()[:200]}")
    except Exception as e:
        print(f"❌ google-chrome 異常: {e}")
    
    # 方法 3: webbrowser.open
    print("\n--- 測試 webbrowser.open ---")
    try:
        print("執行 webbrowser.open...")
        webbrowser.open(test_url)
        print("✅ webbrowser.open 執行完成")
        print("注意：這不表示瀏覽器真的啟動了，只是命令執行了")
    except Exception as e:
        print(f"❌ webbrowser.open 異常: {e}")
    
    # 檢查環境
    print("\n--- 環境檢查 ---")
    print(f"SSH_CONNECTION: {os.getenv('SSH_CONNECTION', '未設定')}")
    print(f"SSH_CLIENT: {os.getenv('SSH_CLIENT', '未設定')}")
    print(f"DISPLAY: {os.getenv('DISPLAY', '未設定')}")
    print(f"平台: {sys.platform}")
    
    # 檢查可用的瀏覽器
    print("\n--- 可用瀏覽器檢查 ---")
    browsers = ['xdg-open', 'google-chrome', 'firefox', 'chromium-browser']
    for browser in browsers:
        try:
            result = subprocess.run(['which', browser], capture_output=True, timeout=2)
            if result.returncode == 0:
                path = result.stdout.decode().strip()
                print(f"✅ {browser}: {path}")
            else:
                print(f"❌ {browser}: 不可用")
        except:
            print(f"❌ {browser}: 檢測失敗")
    
    print("\n=== 總結 ===")
    print("如果上述方法都無法啟動瀏覽器，可能的原因：")
    print("1. SSH remote 環境下無法直接啟動本地瀏覽器")
    print("2. 需要設置 SSH 端口轉發")
    print("3. 需要在本地手動開啟瀏覽器")
    print("")
    print("建議的解決方案：")
    print("1. 在本地終端執行: ssh -L 8765:localhost:8765 user@**************")
    print("2. 然後在本地瀏覽器開啟: http://localhost:8765")

if __name__ == "__main__":
    test_browser_methods()
