# 發佈說明管理系統

此目錄包含了所有版本的結構化發佈說明，支援多語言，採用**雙重架構**來兼顧統一的更新日誌檢視和自動化發佈功能。

## 📁 資料夾結構

```
RELEASE_NOTES/
├── README.md                  # 此說明文檔
├── template.md               # 發佈說明模板
├── CHANGELOG.en.md           # 完整更新歷史（英文）
├── CHANGELOG.zh-TW.md        # 完整更新歷史（繁體中文）
├── CHANGELOG.zh-CN.md        # 完整更新歷史（簡體中文）
├── v2.2.1/                   # 版本特定資料夾（供 GitHub Actions 使用）
│   ├── en.md                 # 英文發佈說明
│   ├── zh-TW.md              # 繁體中文發佈說明
│   └── zh-CN.md              # 簡體中文發佈說明
├── v2.2.0/
│   ├── en.md
│   ├── zh-TW.md
│   └── zh-CN.md
└── ...                       # 其他版本
```

## 🔄 雙重架構優勢

### 📖 CHANGELOG 檔案
- **完整歷史檢視**: 用戶可在單一檔案查看所有版本更新
- **便於搜尋**: 快速找到特定功能的引入版本
- **GitHub 顯示友好**: 更新歷史在專案主頁直接可見

### 📁 版本資料夾  
- **自動化友好**: GitHub Actions 可精確提取特定版本內容
- **維護簡便**: 新版本只需建立新資料夾，無需修改大檔案
- **工具整合**: 各種自動化工具容易解析單一版本檔案

## 🤖 GitHub Actions 整合

GitHub Actions 工作流程會自動執行：
1. **版本檢測**: 從 git 標籤檢測要發佈的版本
2. **發佈說明提取**: 從 `RELEASE_NOTES/vX.Y.Z/` 提取對應的發佈說明
3. **多語言組合**: 將所有語言版本組合成結構化的發佈內容
4. **GitHub Release 發佈**: 將格式化的發佈說明發佈到 GitHub Releases
5. **CHANGELOG 同步**: 自動將新版本內容更新到統一的 CHANGELOG 檔案

## 📝 撰寫發佈說明

### 新版本發佈：
1. **建立版本資料夾**: 為每個版本建立新資料夾（例如 `v2.2.1/`）
2. **添加語言檔案**: 為每種語言添加發佈說明（`en.md`、`zh-TW.md`、`zh-CN.md`）
3. **遵循模板**: 依照 `template.md` 中的模板結構
4. **更新 CHANGELOG**: 將新版本內容添加到各個 CHANGELOG 檔案的頂部
5. **一致格式**: 在各語言中使用一致的表情符號和格式

### 維護作業：
- **版本資料夾**: 供 GitHub Actions 自動化發佈使用
- **CHANGELOG 檔案**: 為用戶和文檔提供統一檢視
- **模板**: 參考 `template.md` 確保結構一致

## 🔧 格式指南

- **表情符號一致性**: 為不同類型的變更使用一致的表情符號前綴
- **簡潔描述**: 保持項目符號簡潔但具描述性  
- **問題引用**: 在適當的地方包含問題引用（例如 `fixes #10`）
- **平行結構**: 在所有語言中保持平行結構
- **時間順序**: 在 CHANGELOG 檔案中將最新版本放在頂部 