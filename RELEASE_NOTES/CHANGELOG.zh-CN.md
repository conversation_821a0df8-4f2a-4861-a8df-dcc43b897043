# 更新日志 (简体中文)

本文件记录了 **MCP Feedback Enhanced** 的所有版本更新内容。

## [v2.2.5] - WSL 环境支持与跨平台增强
# Release v2.2.5 - WSL 环境支持与跨平台增强

## 🌟 亮点
本版本新增了 WSL (Windows Subsystem for Linux) 环境的完整支持，让 WSL 用户能够无缝使用本工具并自动启动 Windows 浏览器，大幅提升跨平台开发体验。

## ✨ 新功能
- 🐧 **WSL 环境检测**: 自动识别 WSL 环境，提供专门的支持逻辑
- 🌐 **智能浏览器启动**: WSL 环境下自动调用 Windows 浏览器，支持多种启动方式
- 🔧 **跨平台测试增强**: 测试功能整合 WSL 检测，提升测试覆盖率

## 🚀 改进功能
- 🎯 **环境检测优化**: 改进远程环境检测逻辑，WSL 不再被误判为远程环境
- 📊 **系统信息增强**: 系统信息工具新增 WSL 环境状态显示
- 🧪 **测试体验提升**: 测试模式下自动尝试启动浏览器，提供更好的测试体验

## 📦 安装与更新
```bash
# 快速测试最新版本
uvx mcp-feedback-enhanced@latest test --gui

# 更新到特定版本
uvx mcp-feedback-enhanced@v2.2.5 test
```

## 🔗 相关链接
- 完整文档: [README.zh-CN.md](../../README.zh-CN.md)
- 问题报告: [GitHub Issues](https://github.com/Minidoracat/mcp-feedback-enhanced/issues)
- 项目首页: [GitHub Repository](https://github.com/Minidoracat/mcp-feedback-enhanced)

---

### ✨ 新功能
- 🐧 **WSL 环境检测**: 自动识别 WSL 环境，提供专门的支持逻辑
- 🌐 **智能浏览器启动**: WSL 环境下自动调用 Windows 浏览器，支持多种启动方式
- 🔧 **跨平台测试增强**: 测试功能整合 WSL 检测，提升测试覆盖率

### 🚀 改进功能
- 🎯 **环境检测优化**: 改进远程环境检测逻辑，WSL 不再被误判为远程环境
- 📊 **系统信息增强**: 系统信息工具新增 WSL 环境状态显示
- 🧪 **测试体验提升**: 测试模式下自动尝试启动浏览器，提供更好的测试体验

---

## [v2.2.4] - GUI 体验优化与问题修复

### 🐛 问题修复
- 🖼️ **图片重复粘贴修复**: 解决 GUI 界面中使用 Ctrl+V 复制粘贴图片时出现重复粘贴的问题
- 🌐 **语系切换修复**: 修复图片设定区域在语言切换时文字没有正确翻译的问题
- 📝 **字体可读性改善**: 调整图片设定区域的字体大小，提升文字可读性

---

## [v2.2.5] - WSL 环境支持与跨平台增强

### ✨ 新功能
- 🐧 **WSL 环境检测**: 自动识别 WSL 环境，提供专门的支持逻辑
- 🌐 **智能浏览器启动**: WSL 环境下自动调用 Windows 浏览器，支持多种启动方式
- 🔧 **跨平台测试增强**: 测试功能整合 WSL 检测，提升测试覆盖率

### 🚀 改进功能
- 🎯 **环境检测优化**: 改进远程环境检测逻辑，WSL 不再被误判为远程环境
- 📊 **系统信息增强**: 系统信息工具新增 WSL 环境状态显示
- 🧪 **测试体验提升**: 测试模式下自动尝试启动浏览器，提供更好的测试体验

---

## [v2.2.4] - GUI 体验优化与问题修复

### 🐛 问题修复
- 🖼️ **图片重复粘贴修复**: 解决 GUI 界面中使用 Ctrl+V 复制粘贴图片时出现重复粘贴的问题
- 🌐 **语系切换修复**: 修复图片设定区域在语言切换时文字没有正确翻译的问题
- 📝 **字体可读性改善**: 调整图片设定区域的字体大小，提升文字可读性

---

## [v2.2.3] - 超时控制与图片设置增强

### ✨ 新功能
- ⏰ **用户超时控制**: 新增可自定义的超时设置功能，支持 30 秒至 2 小时的弹性设置
- ⏱️ **倒数计时器**: 界面顶部显示实时倒数计时器，提供可视化的时间提醒
- 🖼️ **图片大小限制**: 新增图片上传大小限制设置（无限制/1MB/3MB/5MB）
- 🔧 **Base64 兼容模式**: 新增 Base64 详细模式，提升部分 AI 模型的图片识别兼容性
- 🧹 **UV Cache 管理工具**: 新增 `cleanup_cache.py` 脚本，协助管理和清理 UV cache 空间

### 🚀 改进功能
- 📚 **文档结构优化**: 重新整理文档目录结构，将图片移至 `docs/{语言}/images/` 路径
- 📖 **Cache 管理指南**: 新增详细的 UV Cache 管理指南，包含自动化清理方案
- 🎯 **智能兼容性提示**: 当图片上传失败时自动显示 Base64 兼容模式建议

### 🐛 问题修复
- 🛡️ **超时处理优化**: 改进用户自定义超时与 MCP 系统超时的协调机制
- 🖥️ **界面自动关闭**: 修复超时后界面自动关闭和资源清理逻辑
- 📱 **响应式布局**: 优化超时控制组件在小屏幕设备上的显示效果

---

## [v2.2.2] - 超时自动清理修复

### 🐛 问题修复
- 🔄 **超时自动清理**: 修复 GUI/Web UI 在 MCP session timeout (默认 600 秒) 后没有自动关闭的问题
- 🛡️ **资源管理优化**: 改进超时处理机制，确保在超时时正确清理和关闭所有 UI 资源
- ⚡ **超时检测增强**: 加强超时检测逻辑，确保在各种情况下都能正确处理超时事件

---

## [v2.2.1] - 窗口优化与统一设置接口

### 🚀 改进功能
- 🖥️ **窗口大小限制解除**: 解除 GUI 主窗口最小大小限制，从 1000×800 降至 400×300
- 💾 **窗口状态实时保存**: 实现窗口大小与位置的即时保存机制，支持防抖延迟
- ⚙️ **统一设置接口优化**: 改进 GUI 设置版面的配置保存逻辑，避免设置冲突

### 🐛 问题修复
- 🔧 **窗口大小限制**: 解决 GUI 窗口无法调整至小尺寸的问题
- 🛡️ **设置冲突**: 修复设置保存时可能出现的配置冲突问题

---

## [v2.2.0] - 布局与设置界面优化

### ✨ 新功能
- 🎨 **水平布局模式**: GUI 与 Web UI 的合并模式新增摘要与反馈的左右布局选项

### 🚀 改进功能
- 🎨 **设置界面改进**: 优化了 GUI 与 Web UI 的设置页面，提升布局清晰度
- ⌨️ **快捷键完善**: 提交反馈快捷键现已完整支持数字键盘的 Enter 键

### 🐛 问题修复
- 🔧 **图片重复粘贴**: 解决了在 Web UI 文字输入区使用 Ctrl+V 粘贴图片时的重复问题

---

## [v2.1.1] - 窗口定位优化

### ✨ 新功能
- 🖥️ **智能窗口定位**: 新增「总是在主屏幕中心显示窗口」设置选项
- 🌐 **多屏幕支持**: 完美解决 T 字型屏幕排列等复杂多屏幕环境的窗口定位问题
- 💾 **位置记忆**: 自动保存和恢复窗口位置，智能检测窗口可见性

---

## [v2.1.0] - 全面重构版

### 🎨 重大重构
- 🏗️ **全面重构**: GUI 和 Web UI 采用模块化架构
- 📁 **集中管理**: 重新组织文件夹结构，提升维护性
- 🖥️ **界面优化**: 现代化设计和改进的用户体验

### ✨ 新功能
- 🍎 **macOS 界面优化**: 针对 macOS 用户体验进行专项改进
- ⚙️ **功能增强**: 新增设置选项和自动关闭页面功能
- ℹ️ **关于页面**: 新增关于页面，包含版本信息、项目链接和致谢内容

---

## [v2.0.14] - 快捷键与图片功能增强

### 🚀 改进功能
- ⌨️ **增强快捷键**: Ctrl+Enter 支持数字键盘
- 🖼️ **智能图片粘贴**: Ctrl+V 直接粘贴剪贴板图片

---

## [v2.0.9] - 多语言架构重构

### 🔄 重构
- 🌏 **多语言架构重构**: 支持动态载入
- 📁 **语言文件模块化**: 模块化组织语言文件

---

## [v2.0.3] - 编码问题修复

### 🐛 重要修复
- 🛡️ **完全修复中文字符编码问题**: 解决所有中文显示相关问题
- 🔧 **解决 JSON 解析错误**: 修复数据解析错误

---

## [v2.0.0] - Web UI 支持

### 🌟 重大功能
- ✅ **新增 Web UI 支持**: 支持远程环境使用
- ✅ **自动环境检测**: 自动选择合适的界面
- ✅ **WebSocket 即时通讯**: 实现即时双向通讯

---

## 图例说明

| 图标 | 意义 |
|------|------|
| 🌟 | 版本亮点 |
| ✨ | 新功能 |
| 🚀 | 改进功能 |
| 🐛 | 问题修复 |
| 🔄 | 重构变更 |
| 🎨 | 界面优化 |
| ⚙️ | 设置相关 |
| 🖥️ | 窗口相关 |
| 🌐 | 多语言/网络相关 |
| 📁 | 文件结构 |
| ⌨️ | 快捷键 |
| 🖼️ | 图片功能 |

---

**完整项目信息：** [GitHub - mcp-feedback-enhanced](https://github.com/Minidoracat/mcp-feedback-enhanced) 