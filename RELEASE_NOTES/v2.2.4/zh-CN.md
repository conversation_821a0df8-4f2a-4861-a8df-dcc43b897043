# Release v2.2.4 - GUI 体验优化与问题修复

## 🌟 亮点
本版本专注于 GUI 使用体验的优化，修复了图片复制粘贴的重复问题，重新组织了语系文件结构，并改善了界面文字的可读性。

## 🐛 问题修复
- 🖼️ **图片重复粘贴修复**: 解决 GUI 界面中使用 Ctrl+V 复制粘贴图片时出现重复粘贴的问题
- 🌐 **语系切换修复**: 修复图片设定区域在语言切换时文字没有正确翻译的问题
- 📝 **字体可读性改善**: 调整图片设定区域的字体大小，提升文字可读性

## 📦 安装与更新
```bash
# 快速测试最新版本
uvx mcp-feedback-enhanced@latest test --gui

# 更新到特定版本
uvx mcp-feedback-enhanced@v2.2.4 test
```

## 🔗 相关链接
- 完整文档: [README.zh-CN.md](../../README.zh-CN.md)
- 问题报告: [GitHub Issues](https://github.com/Minidoracat/mcp-feedback-enhanced/issues)
- 项目首页: [GitHub Repository](https://github.com/Minidoracat/mcp-feedback-enhanced) 