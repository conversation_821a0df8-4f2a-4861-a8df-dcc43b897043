#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
獨立的瀏覽器啟動器測試
"""

import os
import sys
import subprocess
import socket
from urllib.parse import urlparse

def detect_environment():
    """檢測當前運行環境"""
    env = {
        'platform': sys.platform,
        'is_wsl': is_wsl(),
        'is_ssh_remote': is_ssh_remote(),
        'is_vscode_remote': is_vscode_remote(),
        'is_docker': is_docker(),
        'has_display': bool(os.getenv('DISPLAY')),
        'ssh_client_ip': get_ssh_client_ip(),
        'available_browsers': find_available_browsers(),
    }
    return env

def is_wsl():
    """檢測是否為 WSL 環境"""
    try:
        if os.path.exists('/proc/version'):
            with open('/proc/version', 'r') as f:
                version_info = f.read().lower()
                if 'microsoft' in version_info or 'wsl' in version_info:
                    return True
        
        wsl_env_vars = ['WSL_DISTRO_NAME', 'WSL_INTEROP', 'WSLENV']
        for var in wsl_env_vars:
            if os.getenv(var):
                return True
                
        wsl_paths = ['/mnt/c', '/mnt/d', '/proc/sys/fs/binfmt_misc/WSLInterop']
        for path in wsl_paths:
            if os.path.exists(path):
                return True
    except:
        pass
    return False

def is_ssh_remote():
    """檢測是否為 SSH 遠端環境"""
    ssh_vars = ['SSH_CONNECTION', 'SSH_CLIENT', 'SSH_TTY']
    return any(os.getenv(var) for var in ssh_vars)

def is_vscode_remote():
    """檢測是否為 VS Code Remote 環境"""
    return bool(os.getenv('VSCODE_INJECTION'))

def is_docker():
    """檢測是否為 Docker 容器環境"""
    return os.path.exists('/.dockerenv')

def get_ssh_client_ip():
    """獲取 SSH 客戶端 IP"""
    ssh_client = os.getenv('SSH_CLIENT')
    if ssh_client:
        return ssh_client.split()[0]
    
    ssh_connection = os.getenv('SSH_CONNECTION')
    if ssh_connection:
        return ssh_connection.split()[0]
    
    return None

def find_available_browsers():
    """查找可用的瀏覽器"""
    browsers = []
    
    browser_commands = [
        'google-chrome', 'chrome', 'chromium', 'chromium-browser',
        'firefox', 'firefox-esr',
        'safari', 'edge', 'opera',
        'xdg-open',  # Linux 通用
        'open',      # macOS 通用
        'start',     # Windows 通用
    ]
    
    for cmd in browser_commands:
        try:
            result = subprocess.run(['which', cmd], capture_output=True, timeout=2)
            if result.returncode == 0:
                browsers.append(cmd)
        except:
            pass
    
    return browsers

def test_browser_methods(url):
    """測試各種瀏覽器啟動方法"""
    print(f"\n=== 測試瀏覽器啟動方法 ===")
    print(f"測試 URL: {url}")
    
    methods = [
        ("webbrowser.open", lambda: test_webbrowser_open(url)),
        ("xdg-open", lambda: test_command(['xdg-open', url])),
        ("google-chrome", lambda: test_command(['google-chrome', url])),
        ("SSH 反向連接", lambda: test_ssh_reverse(url)),
    ]
    
    results = []
    for name, method in methods:
        try:
            print(f"\n測試 {name}...", end=" ")
            success = method()
            if success:
                print("✅ 成功")
                results.append((name, True))
            else:
                print("❌ 失敗")
                results.append((name, False))
        except Exception as e:
            print(f"❌ 異常: {e}")
            results.append((name, False))
    
    return results

def test_webbrowser_open(url):
    """測試 webbrowser.open"""
    try:
        import webbrowser
        webbrowser.open(url)
        return True
    except:
        return False

def test_command(cmd):
    """測試命令執行"""
    try:
        result = subprocess.run(cmd, capture_output=True, timeout=5)
        return result.returncode == 0
    except:
        return False

def test_ssh_reverse(url):
    """測試 SSH 反向連接"""
    env = detect_environment()
    if not env['ssh_client_ip']:
        return False
    
    client_ip = env['ssh_client_ip']
    
    # 替換 URL 中的 localhost
    parsed = urlparse(url)
    if parsed.hostname in ['localhost', '127.0.0.1']:
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect((client_ip, 80))
            local_ip = s.getsockname()[0]
            s.close()
            url = url.replace(parsed.netloc, f"{local_ip}:{parsed.port}")
        except:
            pass
    
    # 嘗試在客戶端執行瀏覽器命令
    commands = [
        f'open "{url}"',      # macOS
        f'start "{url}"',     # Windows
        f'xdg-open "{url}"',  # Linux
    ]
    
    for cmd in commands:
        try:
            full_cmd = f'ssh -o ConnectTimeout=2 -o StrictHostKeyChecking=no {client_ip} "{cmd}"'
            result = subprocess.run(full_cmd, shell=True, capture_output=True, timeout=5)
            if result.returncode == 0:
                return True
        except:
            continue
    
    return False

def main():
    """主函數"""
    print("=== 獨立瀏覽器啟動器測試 ===")
    
    # 檢測環境
    env = detect_environment()
    print("\n=== 環境檢測結果 ===")
    for key, value in env.items():
        print(f"{key}: {value}")
    
    # 測試瀏覽器啟動
    test_url = "http://localhost:8765/test"
    results = test_browser_methods(test_url)
    
    # 總結結果
    print(f"\n=== 測試結果總結 ===")
    successful_methods = [name for name, success in results if success]
    
    if successful_methods:
        print(f"✅ 成功的方法: {', '.join(successful_methods)}")
    else:
        print("❌ 所有方法都失敗")
        print("💡 建議手動設置 SSH 端口轉發或使用其他方式")
    
    return len(successful_methods) > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
