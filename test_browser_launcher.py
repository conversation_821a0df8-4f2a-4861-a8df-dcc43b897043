#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試通用瀏覽器啟動器
"""

import sys
import os

# 添加 src 目錄到路徑
sys.path.insert(0, 'src')

def test_browser_launcher():
    """測試瀏覽器啟動器"""
    try:
        from mcp_feedback_enhanced.web.utils.universal_browser import get_universal_launcher
        
        print("=== 通用瀏覽器啟動器測試 ===")
        
        # 獲取啟動器實例
        launcher = get_universal_launcher()
        
        print("\n=== 環境檢測結果 ===")
        env = launcher.environment
        for key, value in env.items():
            print(f"{key}: {value}")
        
        print(f"\n=== 啟動策略 ===")
        for i, strategy in enumerate(launcher.strategies):
            print(f"{i+1}. {strategy.__name__}")
        
        print(f"\n=== 測試瀏覽器啟動 ===")
        test_url = "http://localhost:8765/test"
        print(f"測試 URL: {test_url}")
        
        success = launcher.open_browser(test_url)
        
        if success:
            print("✅ 瀏覽器啟動成功！")
        else:
            print("⚠️ 瀏覽器啟動失敗，但已提供手動操作指引")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_browser_launcher()
    sys.exit(0 if success else 1)
