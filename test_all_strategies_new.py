#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試所有瀏覽器啟動策略 - 新版本
"""

import os
import subprocess
import time

def test_all_browser_strategies():
    """測試所有瀏覽器啟動策略"""
    print("=== 測試所有瀏覽器啟動策略 ===")
    
    test_url = "http://localhost:8765/test"
    
    # 策略 1: xdg-open
    print("\n--- 策略 1: xdg-open ---")
    try:
        result = subprocess.run(['xdg-open', test_url], capture_output=True, timeout=5)
        if result.returncode == 0:
            print("✅ xdg-open 執行成功")
        else:
            print(f"❌ xdg-open 失敗，返回碼: {result.returncode}")
    except Exception as e:
        print(f"❌ xdg-open 異常: {e}")
    
    # 策略 2: google-chrome
    print("\n--- 策略 2: google-chrome ---")
    try:
        cmd = ['google-chrome', '--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu', test_url]
        result = subprocess.run(cmd, capture_output=True, timeout=5)
        if result.returncode == 0:
            print("✅ google-chrome 執行成功")
        else:
            print(f"❌ google-chrome 失敗，返回碼: {result.returncode}")
    except Exception as e:
        print(f"❌ google-chrome 異常: {e}")
    
    # 策略 3: webbrowser.open
    print("\n--- 策略 3: webbrowser.open ---")
    try:
        import webbrowser
        webbrowser.open(test_url)
        print("✅ webbrowser.open 執行完成")
    except Exception as e:
        print(f"❌ webbrowser.open 異常: {e}")
    
    # 策略 4: 檢查環境變數
    print("\n--- 策略 4: 環境變數檢查 ---")
    env_vars = [
        'SSH_CONNECTION',
        'SSH_CLIENT', 
        'DISPLAY',
        'PATH',
    ]
    
    for var in env_vars:
        value = os.getenv(var)
        if value:
            if var == 'PATH':
                # 檢查 PATH 中是否包含 cursor
                if 'cursor' in value.lower():
                    print(f"✅ {var}: 包含 cursor 路徑")
                else:
                    print(f"❌ {var}: 不包含 cursor 路徑")
            else:
                print(f"✅ {var}: {value[:50]}...")
        else:
            print(f"❌ {var}: 未設定")
    
    print("\n=== 測試完成 ===")

if __name__ == "__main__":
    print("開始測試所有瀏覽器啟動策略...")
    
    # 測試所有策略
    test_all_browser_strategies()
    
    print("\n💡 現在請檢查：")
    print("1. 是否有瀏覽器窗口或標籤開啟？")
    print("2. Cursor 右下角是否有端口轉發通知？")
    print("3. 終端面板是否有「端口」標籤？")
