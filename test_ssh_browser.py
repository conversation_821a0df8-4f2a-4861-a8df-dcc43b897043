#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 SSH 環境下的瀏覽器啟動
"""

import os
import sys
import subprocess

def test_ssh_environment():
    """測試 SSH 環境檢測和瀏覽器啟動"""
    print("=== SSH 環境瀏覽器啟動測試 ===")
    
    # 檢查當前環境
    print("\n=== 當前環境檢查 ===")
    ssh_vars = ['SSH_CONNECTION', 'SSH_CLIENT', 'SSH_TTY']
    for var in ssh_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: (未設定)")
    
    print(f"DISPLAY: {os.getenv('DISPLAY', '(未設定)')}")
    print(f"平台: {sys.platform}")
    
    # 檢查可用的瀏覽器
    print("\n=== 可用瀏覽器檢查 ===")
    browsers = ['google-chrome', 'chromium-browser', 'firefox', 'xdg-open']
    available_browsers = []
    
    for browser in browsers:
        try:
            result = subprocess.run(['which', browser], capture_output=True, timeout=2)
            if result.returncode == 0:
                path = result.stdout.decode().strip()
                print(f"✅ {browser}: {path}")
                available_browsers.append(browser)
            else:
                print(f"❌ {browser}: 不可用")
        except:
            print(f"❌ {browser}: 檢測失敗")
    
    # 測試瀏覽器啟動
    print(f"\n=== 瀏覽器啟動測試 ===")
    test_url = "http://localhost:8765/test"
    
    if available_browsers:
        print(f"測試 URL: {test_url}")
        
        for browser in available_browsers[:2]:  # 只測試前兩個
            print(f"\n測試 {browser}...")
            try:
                if browser == 'google-chrome':
                    cmd = [browser, '--no-sandbox', '--disable-dev-shm-usage', test_url]
                elif browser == 'chromium-browser':
                    cmd = [browser, '--no-sandbox', '--disable-dev-shm-usage', test_url]
                else:
                    cmd = [browser, test_url]
                
                print(f"執行命令: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, timeout=5)
                
                if result.returncode == 0:
                    print(f"✅ {browser} 啟動成功")
                else:
                    print(f"❌ {browser} 啟動失敗，返回碼: {result.returncode}")
                    if result.stderr:
                        print(f"   錯誤: {result.stderr.decode()[:200]}")
                        
            except subprocess.TimeoutExpired:
                print(f"⏰ {browser} 啟動超時（可能成功啟動但沒有立即返回）")
            except Exception as e:
                print(f"❌ {browser} 啟動異常: {e}")
    else:
        print("❌ 沒有可用的瀏覽器")
    
    # 測試 webbrowser 模組
    print(f"\n=== webbrowser 模組測試 ===")
    try:
        import webbrowser
        print("✅ webbrowser 模組導入成功")
        
        # 檢查可用的瀏覽器
        browsers = webbrowser._browsers
        if browsers:
            print(f"webbrowser 檢測到的瀏覽器: {list(browsers.keys())}")
        else:
            print("webbrowser 沒有檢測到瀏覽器")
        
        # 嘗試啟動
        print(f"嘗試使用 webbrowser.open 啟動: {test_url}")
        try:
            webbrowser.open(test_url)
            print("✅ webbrowser.open 執行完成（不確定是否真正啟動了瀏覽器）")
        except Exception as e:
            print(f"❌ webbrowser.open 失敗: {e}")
            
    except Exception as e:
        print(f"❌ webbrowser 模組測試失敗: {e}")
    
    # SSH 反向連接測試
    print(f"\n=== SSH 反向連接測試 ===")
    ssh_client = os.getenv('SSH_CLIENT')
    if ssh_client:
        client_ip = ssh_client.split()[0]
        print(f"客戶端 IP: {client_ip}")
        
        # 測試 SSH 連接
        test_commands = [
            f'ssh -o ConnectTimeout=2 -o StrictHostKeyChecking=no -o PasswordAuthentication=no {client_ip} "echo test"',
            f'ssh -o ConnectTimeout=2 -o StrictHostKeyChecking=no -o PasswordAuthentication=no {client_ip} "which open"',
            f'ssh -o ConnectTimeout=2 -o StrictHostKeyChecking=no -o PasswordAuthentication=no {client_ip} "which xdg-open"',
        ]
        
        for cmd in test_commands:
            try:
                print(f"測試: {cmd}")
                result = subprocess.run(cmd, shell=True, capture_output=True, timeout=5)
                if result.returncode == 0:
                    print(f"✅ 成功: {result.stdout.decode().strip()}")
                else:
                    print(f"❌ 失敗: 返回碼 {result.returncode}")
            except Exception as e:
                print(f"❌ 異常: {e}")
    else:
        print("❌ 沒有 SSH_CLIENT 環境變數")
    
    print(f"\n=== 測試完成 ===")

if __name__ == "__main__":
    test_ssh_environment()
