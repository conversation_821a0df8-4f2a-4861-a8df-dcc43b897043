#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 Cursor 自動端口轉發策略
"""

import sys
import os
import time

# 添加 src 目錄到路徑
sys.path.insert(0, 'src')

def test_cursor_strategy():
    """測試 Cursor 自動端口轉發策略"""
    print("=== 測試 Cursor 自動端口轉發策略 ===")
    
    try:
        # 設置環境變數模擬 Cursor 環境
        os.environ['SSH_CLIENT_IP'] = '**************'
        # 如果不是真的 Cursor 環境，我們模擬一個
        if not os.getenv('CURSOR_USER_DATA_DIR'):
            os.environ['CURSOR_USER_DATA_DIR'] = '/tmp/cursor'
            print("模擬 Cursor 環境")
        
        from mcp_feedback_enhanced.web.utils.universal_browser import get_universal_launcher
        
        # 獲取啟動器實例
        launcher = get_universal_launcher()
        
        print("\n=== 環境檢測結果 ===")
        env = launcher.environment
        for key, value in env.items():
            print(f"{key}: {value}")
        
        print(f"\n=== 策略列表 ===")
        for i, strategy in enumerate(launcher.strategies):
            print(f"{i+1}. {strategy.__name__}")
        
        print(f"\n=== 測試 Cursor 自動端口轉發策略 ===")
        test_url = "http://localhost:8765/test"
        print(f"測試 URL: {test_url}")
        
        # 直接測試 Cursor 策略
        cursor_strategy = launcher._launch_cursor_auto_forward
        print("執行 Cursor 自動端口轉發策略...")
        
        start_time = time.time()
        result = cursor_strategy(test_url)
        end_time = time.time()
        
        print(f"\n=== 測試結果 ===")
        print(f"執行時間: {end_time - start_time:.2f} 秒")
        if result:
            print("✅ Cursor 策略報告成功")
            print("請檢查是否有瀏覽器窗口開啟")
        else:
            print("❌ Cursor 策略報告失敗")
        
        # 測試完整的瀏覽器啟動流程
        print(f"\n=== 測試完整瀏覽器啟動流程 ===")
        full_result = launcher.open_browser(test_url)
        
        if full_result:
            print("✅ 完整流程報告成功")
        else:
            print("❌ 完整流程報告失敗")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_redirect_file():
    """檢查重定向文件是否創建"""
    print("\n=== 檢查重定向文件 ===")
    
    redirect_file = os.path.expanduser("~/mcp_redirect.html")
    if os.path.exists(redirect_file):
        print(f"✅ 重定向文件存在: {redirect_file}")
        try:
            with open(redirect_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"文件大小: {len(content)} 字符")
                if 'MCP Feedback Enhanced' in content:
                    print("✅ 文件內容正確")
                else:
                    print("❌ 文件內容異常")
        except Exception as e:
            print(f"❌ 讀取文件失敗: {e}")
    else:
        print(f"❌ 重定向文件不存在: {redirect_file}")

if __name__ == "__main__":
    print("開始測試 Cursor 自動端口轉發策略...")
    
    # 測試策略
    success = test_cursor_strategy()
    
    # 檢查重定向文件
    check_redirect_file()
    
    if success:
        print("\n🎉 測試完成")
        print("\n💡 如果策略成功但瀏覽器仍未開啟，可能的原因：")
        print("1. 需要在真正的 Cursor 環境中測試")
        print("2. 需要有實際運行的 Web 服務器")
        print("3. 可能需要用戶交互（如點擊端口轉發通知）")
    else:
        print("\n❌ 測試失敗")
    
    sys.exit(0 if success else 1)
