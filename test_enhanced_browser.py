#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試增強的瀏覽器啟動功能
"""

import sys
import os
import time

# 添加 src 目錄到路徑
sys.path.insert(0, 'src')

def test_enhanced_browser():
    """測試增強的瀏覽器啟動功能"""
    print("=== 測試增強的瀏覽器啟動功能 ===")
    
    try:
        # 設置環境變數
        os.environ['SSH_CLIENT_IP'] = '**************'
        
        from mcp_feedback_enhanced.web.utils.universal_browser import get_universal_launcher
        
        # 獲取啟動器實例
        launcher = get_universal_launcher()
        
        print("\n=== 環境檢測結果 ===")
        env = launcher.environment
        for key, value in env.items():
            print(f"{key}: {value}")
        
        print(f"\n=== 策略列表 ===")
        for i, strategy in enumerate(launcher.strategies):
            print(f"{i+1}. {strategy.__name__}")
        
        print(f"\n=== 測試激進瀏覽器啟動策略 ===")
        test_url = "http://localhost:8765/test"
        print(f"測試 URL: {test_url}")
        
        # 直接測試激進策略
        aggressive_strategy = launcher._launch_aggressive_browser
        print("執行激進瀏覽器啟動策略...")
        
        start_time = time.time()
        result = aggressive_strategy(test_url)
        end_time = time.time()
        
        print(f"\n=== 激進策略測試結果 ===")
        print(f"執行時間: {end_time - start_time:.2f} 秒")
        if result:
            print("✅ 激進策略報告成功")
            print("請檢查是否有瀏覽器窗口開啟")
        else:
            print("❌ 激進策略報告失敗")
        
        # 測試完整的瀏覽器啟動流程
        print(f"\n=== 測試完整瀏覽器啟動流程 ===")
        full_result = launcher.open_browser(test_url)
        
        if full_result:
            print("✅ 完整流程報告成功")
        else:
            print("❌ 完整流程報告失敗")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_created_files():
    """檢查創建的文件"""
    print("\n=== 檢查創建的文件 ===")
    
    files_to_check = [
        "~/mcp_redirect.html",
        "~/mcp_browser_guide.txt",
    ]
    
    for file_path in files_to_check:
        full_path = os.path.expanduser(file_path)
        if os.path.exists(full_path):
            print(f"✅ 文件存在: {full_path}")
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(f"   文件大小: {len(content)} 字符")
                    if len(content) > 100:
                        print(f"   內容預覽: {content[:100]}...")
            except Exception as e:
                print(f"   ❌ 讀取文件失敗: {e}")
        else:
            print(f"❌ 文件不存在: {full_path}")

def test_environment_detection():
    """測試環境檢測"""
    print("\n=== 測試環境檢測 ===")
    
    try:
        from mcp_feedback_enhanced.web.utils.universal_browser import get_universal_launcher
        
        launcher = get_universal_launcher()
        
        print("測試 VS Code/Cursor 環境檢測...")
        is_vscode = launcher._is_vscode_remote()
        print(f"VS Code/Cursor 環境檢測結果: {is_vscode}")
        
        return True
        
    except Exception as e:
        print(f"❌ 環境檢測測試失敗: {e}")
        return False

if __name__ == "__main__":
    print("開始測試增強的瀏覽器啟動功能...")
    
    # 測試環境檢測
    env_success = test_environment_detection()
    
    # 測試增強功能
    browser_success = test_enhanced_browser()
    
    # 檢查創建的文件
    check_created_files()
    
    if env_success and browser_success:
        print("\n🎉 所有測試完成")
        print("\n💡 如果瀏覽器仍未自動啟動，請：")
        print("1. 檢查是否有瀏覽器安全提示需要確認")
        print("2. 查看創建的重定向文件是否能手動開啟")
        print("3. 嘗試手動設置 SSH 端口轉發")
    else:
        print("\n❌ 部分測試失敗")
    
    sys.exit(0 if (env_success and browser_success) else 1)
