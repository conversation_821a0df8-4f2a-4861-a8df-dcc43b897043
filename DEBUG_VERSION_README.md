# MCP Feedback Enhanced - 調試版本

## 概述

這是一個專門用於調試瀏覽器啟動問題的版本，會嘗試所有可能的策略並提供詳細的調試信息。

## 主要變更

### 1. 配置變更
- **預設不再是單次嘗試** - `BROWSER_SINGLE_ATTEMPT` 預設為 `false`
- **所有策略都會被嘗試** - 不會在第一個成功後停止
- **詳細調試信息** - 每個策略都會輸出詳細的執行過程

### 2. 新增的策略

#### 實驗性方法 (`_launch_experimental_methods`)
1. **HTTP 重定向** - 創建重定向 HTML 文件
2. **臨時 HTML 文件** - 在用戶主目錄創建 HTML 文件
3. **curl 觸發** - 使用 curl 測試連接
4. **netcat 觸發** - 使用 netcat 測試端口

#### 增強的現有策略
- **SSH 反向連接** - 更詳細的錯誤報告
- **本地瀏覽器啟動** - 在 SSH 環境下嘗試更多方法
- **手動回退** - 提供更實用的操作指引

### 3. 調試功能

#### 詳細日誌
- 每個策略的執行狀態
- 命令執行的返回碼和錯誤信息
- 異常的完整堆疊追蹤

#### 策略統計
- 顯示總共有多少策略
- 顯示當前執行的策略編號
- 顯示成功/失敗的策略數量

## 使用方法

### 1. 構建和測試
```bash
# 使用提供的腳本
./build_and_test.sh

# 或手動執行
uv build
python3 test_all_strategies.py
```

### 2. 環境變數控制
```bash
# 強制嘗試所有策略（預設）
export BROWSER_SINGLE_ATTEMPT=false

# 只嘗試第一個成功的策略
export BROWSER_SINGLE_ATTEMPT=true

# 啟用詳細調試
export BROWSER_DEBUG=true
export MCP_DEBUG=true
```

### 3. 測試 Web UI
```bash
# 測試 Web UI（會嘗試所有瀏覽器啟動策略）
uvx --with-editable . mcp-feedback-enhanced test --web
```

## 預期的調試輸出

### 成功的策略會顯示：
```
✅ 策略 _launch_local_browser 回報成功
```

### 失敗的策略會顯示：
```
❌ 策略 _launch_ssh_reverse 回報失敗
```

### 異常的策略會顯示：
```
💥 策略 _launch_experimental_methods 發生異常: [詳細錯誤信息]
```

## 策略執行順序

1. **VS Code/Cursor Remote** - 如果檢測到 VS Code 環境
2. **SSH 反向連接** - 嘗試在客戶端啟動瀏覽器
3. **SSH 端口轉發提示** - 提供手動操作指引
4. **WSL Windows 瀏覽器** - 如果在 WSL 環境
5. **本地瀏覽器啟動** - 嘗試各種本地命令
6. **系統預設啟動** - 使用系統預設方法
7. **實驗性方法** - 嘗試創新的啟動方法
8. **手動回退** - 提供詳細的手動操作指引

## 調試重點

### 需要觀察的關鍵信息：

1. **環境檢測是否正確**
   ```
   'is_ssh_remote': True/False
   'ssh_client_ip': '192.168.x.x' 或 None
   ```

2. **哪些策略真正成功**
   - 看到 `✅` 標記的策略
   - 注意是否真的啟動了瀏覽器

3. **失敗的原因**
   - SSH 連接被拒絕（正常）
   - 命令不存在
   - 返回碼非零

4. **實驗性方法的效果**
   - 是否能創建臨時文件
   - 是否能執行 curl/netcat

## 下一步

根據調試結果，我們將：

1. **識別有效的策略** - 保留真正能啟動瀏覽器的方法
2. **移除無效的策略** - 刪除不起作用的方法
3. **優化策略順序** - 將最有效的方法放在前面
4. **恢復單次嘗試模式** - 避免重複啟動瀏覽器

## 測試環境

- **SSH Remote** - Windows 客戶端 SSH 到 Ubuntu 服務器
- **Cursor 1.0** - 新版本的 Cursor 編輯器
- **無 GUI 環境** - 服務器沒有 DISPLAY 環境變數

請在測試後提供詳細的日誌輸出，特別是：
- 哪些策略報告成功
- 是否真的啟動了本地瀏覽器
- 任何異常或錯誤信息
