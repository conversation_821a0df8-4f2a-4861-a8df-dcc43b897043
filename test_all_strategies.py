#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試所有瀏覽器啟動策略
"""

import sys
import os

# 添加 src 目錄到路徑
sys.path.insert(0, 'src')

def test_all_strategies():
    """測試所有瀏覽器啟動策略"""
    print("=== 測試所有瀏覽器啟動策略 ===")
    
    try:
        # 設置調試模式
        os.environ['BROWSER_SINGLE_ATTEMPT'] = 'false'
        os.environ['BROWSER_DEBUG'] = 'true'
        
        from mcp_feedback_enhanced.web.utils.universal_browser import get_universal_launcher
        
        # 獲取啟動器實例
        launcher = get_universal_launcher()
        
        print("\n=== 環境檢測結果 ===")
        env = launcher.environment
        for key, value in env.items():
            print(f"{key}: {value}")
        
        print(f"\n=== 可用策略列表 ===")
        for i, strategy in enumerate(launcher.strategies):
            print(f"{i+1}. {strategy.__name__}")
        
        print(f"\n=== 開始測試瀏覽器啟動 ===")
        test_url = "http://localhost:8765/test"
        print(f"測試 URL: {test_url}")
        print(f"單次嘗試模式: {launcher.config.is_single_attempt()}")
        
        # 執行瀏覽器啟動測試
        success = launcher.open_browser(test_url)
        
        print(f"\n=== 測試結果 ===")
        if success:
            print("✅ 至少有一個策略報告成功")
        else:
            print("❌ 所有策略都失敗")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_strategies():
    """單獨測試每個策略"""
    print("\n=== 單獨測試每個策略 ===")
    
    try:
        from mcp_feedback_enhanced.web.utils.universal_browser import get_universal_launcher
        
        launcher = get_universal_launcher()
        test_url = "http://localhost:8765/test"
        
        for i, strategy in enumerate(launcher.strategies):
            print(f"\n--- 測試策略 {i+1}: {strategy.__name__} ---")
            try:
                result = strategy(test_url)
                if result:
                    print(f"✅ {strategy.__name__} 成功")
                else:
                    print(f"❌ {strategy.__name__} 失敗")
            except Exception as e:
                print(f"💥 {strategy.__name__} 異常: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 單獨測試失敗: {e}")
        return False

if __name__ == "__main__":
    print("開始瀏覽器啟動策略測試...")
    
    # 測試所有策略
    success1 = test_all_strategies()
    
    # 單獨測試每個策略
    success2 = test_individual_strategies()
    
    if success1 and success2:
        print("\n🎉 所有測試完成")
    else:
        print("\n❌ 部分測試失敗")
    
    sys.exit(0 if (success1 and success2) else 1)
