## 🌐 Multi-Language Release Notes

### 🇺🇸 English
# Release v2.2.5 - WSL Environment Support & Cross-Platform Enhancement

## 🌟 Highlights
This version introduces comprehensive support for WSL (Windows Subsystem for Linux) environments, enabling WSL users to seamlessly use this tool with automatic Windows browser launching, significantly improving cross-platform development experience.

## ✨ New Features
- 🐧 **WSL Environment Detection**: Automatically identifies WSL environments and provides specialized support logic
- 🌐 **Smart Browser Launching**: Automatically invokes Windows browser in WSL environments with multiple launch methods
- 🔧 **Cross-Platform Testing Enhancement**: Test functionality integrates WSL detection for improved test coverage

## 🚀 Improvements
- 🎯 **Environment Detection Optimization**: Improved remote environment detection logic, WSL no longer misidentified as remote environment
- 📊 **System Information Enhancement**: System information tool now displays WSL environment status
- 🧪 **Testing Experience Improvement**: Test mode automatically attempts browser launching for better testing experience

## 📦 Installation & Update
```bash
# Quick test latest version
uvx mcp-feedback-enhanced@latest test --gui

# Update to specific version
uvx mcp-feedback-enhanced@v2.2.5 test
```

## 🔗 Related Links
- Full Documentation: [README.md](../../README.md)
- Issue Reports: [GitHub Issues](https://github.com/Minidoracat/mcp-feedback-enhanced/issues)
- Project Homepage: [GitHub Repository](https://github.com/Minidoracat/mcp-feedback-enhanced)

---

### 🇹🇼 繁體中文
# Release v2.2.5 - WSL 環境支援與跨平台增強

## 🌟 亮點
本版本新增了 WSL (Windows Subsystem for Linux) 環境的完整支援，讓 WSL 用戶能夠無縫使用本工具並自動啟動 Windows 瀏覽器，大幅提升跨平台開發體驗。

## ✨ 新功能
- 🐧 **WSL 環境檢測**: 自動識別 WSL 環境，提供專門的支援邏輯
- 🌐 **智能瀏覽器啟動**: WSL 環境下自動調用 Windows 瀏覽器，支援多種啟動方式
- 🔧 **跨平台測試增強**: 測試功能整合 WSL 檢測，提升測試覆蓋率

## 🚀 改進功能
- 🎯 **環境檢測優化**: 改進遠端環境檢測邏輯，WSL 不再被誤判為遠端環境
- 📊 **系統資訊增強**: 系統資訊工具新增 WSL 環境狀態顯示
- 🧪 **測試體驗提升**: 測試模式下自動嘗試啟動瀏覽器，提供更好的測試體驗

## 📦 安裝與更新
```bash
# 快速測試最新版本
uvx mcp-feedback-enhanced@latest test --gui

# 更新到特定版本
uvx mcp-feedback-enhanced@v2.2.5 test
```

## 🔗 相關連結
- 完整文檔: [README.zh-TW.md](../../README.zh-TW.md)
- 問題回報: [GitHub Issues](https://github.com/Minidoracat/mcp-feedback-enhanced/issues)
- 專案首頁: [GitHub Repository](https://github.com/Minidoracat/mcp-feedback-enhanced)

---

### 🇨🇳 简体中文
# Release v2.2.5 - WSL 环境支持与跨平台增强

## 🌟 亮点
本版本新增了 WSL (Windows Subsystem for Linux) 环境的完整支持，让 WSL 用户能够无缝使用本工具并自动启动 Windows 浏览器，大幅提升跨平台开发体验。

## ✨ 新功能
- 🐧 **WSL 环境检测**: 自动识别 WSL 环境，提供专门的支持逻辑
- 🌐 **智能浏览器启动**: WSL 环境下自动调用 Windows 浏览器，支持多种启动方式
- 🔧 **跨平台测试增强**: 测试功能整合 WSL 检测，提升测试覆盖率

## 🚀 改进功能
- 🎯 **环境检测优化**: 改进远程环境检测逻辑，WSL 不再被误判为远程环境
- 📊 **系统信息增强**: 系统信息工具新增 WSL 环境状态显示
- 🧪 **测试体验提升**: 测试模式下自动尝试启动浏览器，提供更好的测试体验

## 📦 安装与更新
```bash
# 快速测试最新版本
uvx mcp-feedback-enhanced@latest test --gui

# 更新到特定版本
uvx mcp-feedback-enhanced@v2.2.5 test
```

## 🔗 相关链接
- 完整文档: [README.zh-CN.md](../../README.zh-CN.md)
- 问题报告: [GitHub Issues](https://github.com/Minidoracat/mcp-feedback-enhanced/issues)
- 项目首页: [GitHub Repository](https://github.com/Minidoracat/mcp-feedback-enhanced)

---

## 📦 Installation & Update

```bash
# Quick test latest version
uvx mcp-feedback-enhanced@latest test

# Update to this specific version
uvx mcp-feedback-enhanced@v2.2.5 test
```

## 🔗 Links
- **Documentation**: [README.md](https://github.com/Minidoracat/mcp-feedback-enhanced/blob/main/README.md)
- **Full Changelog**: [CHANGELOG](https://github.com/Minidoracat/mcp-feedback-enhanced/blob/main/RELEASE_NOTES/)
- **Issues**: [GitHub Issues](https://github.com/Minidoracat/mcp-feedback-enhanced/issues)

---
**Release automatically generated from RELEASE_NOTES system** 🤖
