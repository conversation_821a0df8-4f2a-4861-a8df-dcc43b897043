#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試激進的瀏覽器啟動策略
"""

import os
import subprocess
import time

def test_aggressive_browser_strategy():
    """測試激進的瀏覽器啟動策略"""
    print("=== 測試激進的瀏覽器啟動策略 ===")
    
    test_url = "http://localhost:8765/test"
    success_methods = []
    
    # 方法 1: 創建並開啟重定向 HTML 文件
    try:
        print("\n方法 1: 創建重定向 HTML 文件")
        
        redirect_html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="refresh" content="0;url={test_url}">
    <title>MCP Feedback Enhanced</title>
    <script>
        window.location.href = '{test_url}';
    </script>
</head>
<body>
    <h1>MCP Feedback Enhanced</h1>
    <p>正在重定向到 Web UI...</p>
    <p><a href="{test_url}">點擊這裡手動開啟</a></p>
</body>
</html>"""
        
        redirect_file = os.path.expanduser("~/mcp_redirect_test.html")
        with open(redirect_file, 'w', encoding='utf-8') as f:
            f.write(redirect_html)
        
        print(f"✅ 重定向文件創建成功: {redirect_file}")
        
        # 嘗試開啟重定向文件
        redirect_commands = [
            ['xdg-open', redirect_file],
            ['google-chrome', redirect_file],
            ['firefox', redirect_file],
        ]
        
        for cmd in redirect_commands:
            try:
                print(f"嘗試命令: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, timeout=5)
                if result.returncode == 0:
                    print(f"✅ 重定向文件開啟成功: {cmd[0]}")
                    success_methods.append(f"重定向文件 ({cmd[0]})")
                    time.sleep(2)  # 給瀏覽器時間啟動
                    break
                else:
                    print(f"❌ 命令失敗: {cmd[0]}, 返回碼: {result.returncode}")
            except FileNotFoundError:
                print(f"❌ 命令不存在: {cmd[0]}")
            except subprocess.TimeoutExpired:
                print(f"⏰ 命令超時: {cmd[0]} (可能成功啟動)")
                success_methods.append(f"重定向文件 ({cmd[0]} - 超時)")
                break
            except Exception as e:
                print(f"❌ 命令異常: {cmd[0]}, 錯誤: {e}")
                
    except Exception as e:
        print(f"重定向文件方法失敗: {e}")
    
    # 方法 2: 嘗試所有可能的瀏覽器命令
    try:
        print("\n方法 2: 嘗試所有瀏覽器命令")
        
        browser_commands = [
            ['google-chrome', '--new-window', test_url],
            ['google-chrome', '--app=' + test_url],
            ['firefox', '--new-window', test_url],
            ['firefox', '--new-tab', test_url],
            ['xdg-open', test_url],
        ]
        
        for cmd in browser_commands:
            try:
                print(f"嘗試命令: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, timeout=5)
                if result.returncode == 0:
                    print(f"✅ 瀏覽器命令成功: {' '.join(cmd)}")
                    success_methods.append(f"瀏覽器命令 ({cmd[0]})")
                    time.sleep(2)
                    break
                else:
                    print(f"❌ 命令失敗: {cmd[0]}, 返回碼: {result.returncode}")
            except FileNotFoundError:
                print(f"❌ 命令不存在: {cmd[0]}")
            except subprocess.TimeoutExpired:
                print(f"⏰ 命令超時: {cmd[0]} (可能成功啟動)")
                success_methods.append(f"瀏覽器命令 ({cmd[0]} - 超時)")
                break
            except Exception as e:
                print(f"❌ 命令異常: {cmd[0]}, 錯誤: {e}")
                
    except Exception as e:
        print(f"瀏覽器命令方法失敗: {e}")
    
    # 方法 3: Python webbrowser 模組
    try:
        print("\n方法 3: Python webbrowser 模組")
        
        import webbrowser
        
        print(f"嘗試 webbrowser.open: {test_url}")
        webbrowser.open(test_url)
        print("✅ webbrowser.open 執行完成")
        success_methods.append("webbrowser (預設)")
        
    except Exception as e:
        print(f"webbrowser 方法失敗: {e}")
    
    # 總結結果
    print(f"\n=== 測試結果總結 ===")
    if success_methods:
        print(f"✅ 激進策略成功，使用的方法: {', '.join(success_methods)}")
        print("請檢查是否有瀏覽器窗口開啟")
        return True
    else:
        print("❌ 激進策略的所有方法都失敗")
        return False

def cleanup_test_files():
    """清理測試文件"""
    print("\n=== 清理測試文件 ===")
    
    test_files = [
        "~/mcp_redirect_test.html",
    ]
    
    for file_path in test_files:
        full_path = os.path.expanduser(file_path)
        try:
            if os.path.exists(full_path):
                os.remove(full_path)
                print(f"✅ 已刪除: {full_path}")
            else:
                print(f"文件不存在: {full_path}")
        except Exception as e:
            print(f"❌ 刪除失敗: {full_path}, 錯誤: {e}")

if __name__ == "__main__":
    print("開始測試激進的瀏覽器啟動策略...")
    
    # 測試激進策略
    success = test_aggressive_browser_strategy()
    
    # 清理測試文件
    cleanup_test_files()
    
    if success:
        print("\n🎉 測試完成")
        print("\n💡 如果看到瀏覽器啟動，說明策略有效")
        print("💡 如果沒有看到瀏覽器，可能是 SSH remote 環境限制")
    else:
        print("\n❌ 測試失敗")
    
    print("\n建議的解決方案：")
    print("1. 在本地終端執行: ssh -L 8765:localhost:8765 user@**************")
    print("2. 然後在本地瀏覽器開啟: http://localhost:8765")
