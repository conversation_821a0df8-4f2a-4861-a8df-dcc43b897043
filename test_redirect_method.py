#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試重定向方法
"""

import os
import subprocess
import time

def test_redirect_method():
    """測試重定向方法"""
    print("=== 測試重定向方法 ===")
    
    test_url = "http://localhost:8765/test"
    
    # 創建重定向 HTML 文件
    redirect_html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="refresh" content="1;url={test_url}">
    <title>MCP Feedback Enhanced - 自動重定向</title>
    <script>
        setTimeout(function() {{
            window.location.href = '{test_url}';
        }}, 1000);
    </script>
</head>
<body>
    <h1>MCP Feedback Enhanced</h1>
    <p>正在重定向到 Web UI...</p>
    <p>如果沒有自動跳轉，請點擊: <a href="{test_url}">{test_url}</a></p>
    <p>這是一個測試頁面，用於驗證重定向功能。</p>
</body>
</html>"""
    
    redirect_file = os.path.expanduser("~/mcp_redirect_test.html")
    
    try:
        print(f"創建重定向文件: {redirect_file}")
        with open(redirect_file, 'w', encoding='utf-8') as f:
            f.write(redirect_html)
        
        print("✅ 重定向文件創建成功")
        print(f"文件大小: {len(redirect_html)} 字符")
        
        # 嘗試用不同的方法開啟文件
        commands = [
            ['xdg-open', redirect_file],
            ['google-chrome', redirect_file],
            ['firefox', redirect_file],
        ]
        
        success_count = 0
        
        for cmd in commands:
            try:
                print(f"\n嘗試命令: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, timeout=5)
                
                if result.returncode == 0:
                    print(f"✅ 命令執行成功: {cmd[0]}")
                    success_count += 1
                    
                    # 給一些時間讓瀏覽器啟動
                    print("等待 3 秒讓瀏覽器啟動...")
                    time.sleep(3)
                    break
                else:
                    print(f"❌ 命令執行失敗: {cmd[0]}, 返回碼: {result.returncode}")
                    if result.stderr:
                        stderr = result.stderr.decode()[:200]
                        print(f"錯誤信息: {stderr}")
                        
            except FileNotFoundError:
                print(f"❌ 命令不存在: {cmd[0]}")
            except subprocess.TimeoutExpired:
                print(f"⏰ 命令超時: {cmd[0]} (可能成功啟動但沒有立即返回)")
                success_count += 1
                break
            except Exception as e:
                print(f"❌ 命令異常: {cmd[0]}, 錯誤: {e}")
        
        print(f"\n=== 測試結果 ===")
        if success_count > 0:
            print(f"✅ 有 {success_count} 個命令執行成功")
            print("請檢查是否有瀏覽器窗口開啟")
            print("如果有，應該會顯示重定向頁面")
        else:
            print("❌ 所有命令都失敗")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False
    finally:
        # 清理測試文件
        try:
            if os.path.exists(redirect_file):
                os.remove(redirect_file)
                print(f"已清理測試文件: {redirect_file}")
        except:
            pass

def test_webbrowser_method():
    """測試 webbrowser 方法"""
    print("\n=== 測試 webbrowser 方法 ===")
    
    try:
        import webbrowser
        test_url = "http://localhost:8765/test"
        
        print(f"嘗試 webbrowser.open: {test_url}")
        webbrowser.open(test_url)
        print("✅ webbrowser.open 執行完成")
        print("注意：這不表示瀏覽器真的啟動了")
        
        return True
        
    except Exception as e:
        print(f"❌ webbrowser 測試失敗: {e}")
        return False

if __name__ == "__main__":
    print("開始測試瀏覽器啟動方法...")
    
    # 測試重定向方法
    redirect_success = test_redirect_method()
    
    # 測試 webbrowser 方法
    webbrowser_success = test_webbrowser_method()
    
    print(f"\n=== 總結 ===")
    print(f"重定向方法: {'✅ 成功' if redirect_success else '❌ 失敗'}")
    print(f"webbrowser 方法: {'✅ 成功' if webbrowser_success else '❌ 失敗'}")
    
    if redirect_success or webbrowser_success:
        print("\n💡 如果沒有看到瀏覽器啟動，可能的原因：")
        print("1. SSH remote 環境下無法直接啟動本地瀏覽器")
        print("2. 需要設置 SSH 端口轉發")
        print("3. 需要在真正的 GUI 環境中測試")
    
    print("\n建議的解決方案：")
    print("1. 在本地終端執行: ssh -L 8765:localhost:8765 user@**************")
    print("2. 然後在本地瀏覽器開啟: http://localhost:8765")
