#!/bin/bash

echo "=== MCP Feedback Enhanced 構建和測試腳本 ==="

# 設置調試模式
export BROWSER_SINGLE_ATTEMPT=false
export BROWSER_DEBUG=true
export MCP_DEBUG=true

echo "1. 清理舊的構建..."
rm -rf dist/ build/ *.egg-info/

echo "2. 構建套件..."
uv build

if [ $? -eq 0 ]; then
    echo "✅ 構建成功"
    
    echo "3. 列出構建產物..."
    ls -la dist/
    
    echo "4. 測試瀏覽器啟動策略..."
    python3 test_all_strategies.py
    
    echo "5. 測試 Web UI..."
    timeout 15 uvx --with-editable . mcp-feedback-enhanced test --web
    
    echo "✅ 構建和測試完成"
    echo "📦 構建產物位於 dist/ 目錄"
    echo "💡 可以使用以下命令安裝："
    echo "   pip install dist/mcp_feedback_enhanced-*.whl"
else
    echo "❌ 構建失敗"
    exit 1
fi
