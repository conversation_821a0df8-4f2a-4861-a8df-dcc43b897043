# 通用瀏覽器啟動器使用說明

## 概述

mcp-feedback-enhanced 現在包含了一個強大的通用瀏覽器啟動器，能夠在各種環境下自動啟動瀏覽器，包括：

- **本地環境**（Windows、macOS、Linux）
- **SSH Remote 環境**
- **WSL 環境**
- **VS Code Remote 環境**
- **Docker 容器環境**

## 主要功能

### 🌐 智能環境檢測
- 自動檢測當前運行環境
- 根據環境選擇最佳的瀏覽器啟動策略
- 支援多種瀏覽器和啟動方法

### 🔧 多策略啟動
1. **VS Code Remote** - 利用 VS Code 的端口轉發功能
2. **SSH 反向連接** - 嘗試在客戶端機器上啟動瀏覽器
3. **WSL Windows 瀏覽器** - 在 WSL 環境中啟動 Windows 瀏覽器
4. **本地瀏覽器** - 標準的本地瀏覽器啟動
5. **系統預設** - 使用系統預設的 URL 處理程式
6. **手動回退** - 提供手動操作指引

### ⚙️ 可配置選項
通過環境變數自定義瀏覽器行為：

```bash
# 基本設定
export BROWSER_SINGLE_ATTEMPT=true     # 只嘗試一次啟動（避免重複）
export BROWSER_TIMEOUT=10              # 啟動超時時間（秒）
export BROWSER_SHOW_MANUAL=true        # 顯示手動操作指引

# SSH 相關
export BROWSER_SSH_REVERSE=true        # 啟用 SSH 反向連接
export BROWSER_SSH_TIMEOUT=5           # SSH 連接超時時間

# WSL 相關
export BROWSER_WSL_WINDOWS=true        # WSL 使用 Windows 瀏覽器

# 其他
export BROWSER_PREFERRED=google-chrome # 偏好的瀏覽器
export BROWSER_DEBUG=true              # 調試模式
```

## 使用方法

### 基本使用
```bash
# 啟動 Web UI 測試（會自動開啟瀏覽器）
uvx --with-editable . mcp-feedback-enhanced test --web
```

### 避免重複啟動
如果您遇到瀏覽器重複啟動的問題，可以設定：
```bash
export BROWSER_SINGLE_ATTEMPT=true
uvx --with-editable . mcp-feedback-enhanced test --web
```

### SSH Remote 環境
在 SSH remote 環境下，啟動器會：
1. 嘗試在客戶端機器上啟動瀏覽器
2. 提供端口轉發的設定建議
3. 顯示手動操作的 URL

建議的 SSH 連接方式：
```bash
# 設定端口轉發
ssh -L 8765:localhost:8765 user@remote-host

# 或者動態添加
ssh -O forward -L 8765:localhost:8765 user@remote-host
```

### WSL 環境
在 WSL 環境下，啟動器會自動：
1. 嘗試使用 `cmd.exe` 啟動 Windows 瀏覽器
2. 嘗試使用 `powershell.exe` 啟動
3. 嘗試使用 `wslview`（如果安裝了 wslu）

## 故障排除

### 瀏覽器安全提示
如果看到瀏覽器顯示安全提示（如「您在連接埠 8765 上執行的應用程式可供使用」），這是正常的瀏覽器安全機制。點擊「在瀏覽器中開啟」即可。

### 重複啟動問題
設定 `BROWSER_SINGLE_ATTEMPT=true` 可以避免多個瀏覽器實例同時啟動。

### SSH 環境無法啟動
1. 確保 SSH 連接時設定了端口轉發
2. 檢查客戶端機器是否有可用的瀏覽器
3. 手動複製 URL 到瀏覽器

### 調試模式
啟用調試模式查看詳細的啟動過程：
```bash
export BROWSER_DEBUG=true
export MCP_DEBUG=true
uvx --with-editable . mcp-feedback-enhanced test --web
```

## 配置說明查看

查看完整的配置選項說明：
```bash
python3 -c "from src.mcp_feedback_enhanced.web.utils.browser_config import print_browser_config_help; print_browser_config_help()"
```

## 技術細節

### 環境檢測邏輯
- **WSL**: 檢查 `/proc/version`、WSL 環境變數、WSL 特有路徑
- **SSH Remote**: 檢查 `SSH_CONNECTION`、`SSH_CLIENT`、`SSH_TTY` 環境變數
- **VS Code Remote**: 檢查 `VSCODE_INJECTION` 環境變數
- **Docker**: 檢查 `/.dockerenv` 文件

### 瀏覽器查找
自動查找系統中可用的瀏覽器：
- Chrome/Chromium 系列
- Firefox 系列
- 系統預設瀏覽器（xdg-open、open、start）

### 安全考量
- 所有 SSH 連接都使用短超時時間
- 不會儲存或傳輸敏感資訊
- 遵循瀏覽器的安全機制

## 更新日誌

### v2.2.6
- ✨ 新增通用瀏覽器啟動器
- 🔧 支援多環境自動檢測
- ⚙️ 添加可配置選項
- 🐛 修復 SSH remote 環境下的啟動問題
- 📚 完善文檔和使用說明
