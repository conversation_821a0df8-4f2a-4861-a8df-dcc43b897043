#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
獨立的環境檢測測試
"""

import os
import sys

def test_environment():
    """測試環境檢測"""
    print("=== 獨立環境檢測測試 ===")
    
    print("\n=== 當前環境變數 ===")
    env_vars = [
        'SSH_CONNECTION', 'SSH_CLIENT', 'SSH_TTY',
        'VSCODE_INJECTION', 'VSCODE_IPC_HOOK', 'TERM_PROGRAM',
        'CURSOR_USER_DATA_DIR', 'CURSOR_LOGS_DIR',
        'DISPLAY', 'WSL_DISTRO_NAME', 'REMOTE_CONTAINERS',
        'CODESPACES', 'GITPOD_WORKSPACE_ID'
    ]
    
    for var in env_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: (未設定)")
    
    print("\n=== 環境檢測邏輯 ===")
    
    # WSL 檢測
    is_wsl = False
    try:
        if os.path.exists('/proc/version'):
            with open('/proc/version', 'r') as f:
                version_info = f.read().lower()
                if 'microsoft' in version_info or 'wsl' in version_info:
                    is_wsl = True
                    print(f"✅ WSL 檢測: True (/proc/version 包含 WSL 標識)")
                else:
                    print(f"❌ WSL 檢測: False (/proc/version 不包含 WSL 標識)")
        
        wsl_env_vars = ['WSL_DISTRO_NAME', 'WSL_INTEROP', 'WSLENV']
        for var in wsl_env_vars:
            if os.getenv(var):
                is_wsl = True
                print(f"✅ WSL 檢測: True (環境變數 {var})")
                break
    except Exception as e:
        print(f"❌ WSL 檢測異常: {e}")
    
    # SSH Remote 檢測
    is_ssh_remote = False
    ssh_vars = ['SSH_CONNECTION', 'SSH_CLIENT', 'SSH_TTY']
    for var in ssh_vars:
        if os.getenv(var):
            is_ssh_remote = True
            print(f"✅ SSH Remote 檢測: True (環境變數 {var})")
            break
    
    if not is_ssh_remote:
        # 其他遠端環境檢測
        remote_indicators = [
            'REMOTE_CONTAINERS',
            'CODESPACES',
            'GITPOD_WORKSPACE_ID',
        ]
        for var in remote_indicators:
            if os.getenv(var):
                is_ssh_remote = True
                print(f"✅ 遠端環境檢測: True (環境變數 {var})")
                break
    
    if not is_ssh_remote:
        # 檢查無 DISPLAY 的 Linux 環境
        if (sys.platform.startswith('linux') and 
            not os.getenv('DISPLAY') and 
            not is_wsl):
            is_ssh_remote = True
            print(f"✅ 遠端環境檢測: True (Linux 無 DISPLAY)")
    
    if not is_ssh_remote:
        # 檢查 Docker
        if os.path.exists('/.dockerenv'):
            is_ssh_remote = True
            print(f"✅ 遠端環境檢測: True (Docker 容器)")
    
    if not is_ssh_remote:
        print(f"❌ SSH/遠端環境檢測: False")
    
    # VS Code/Cursor 檢測
    is_vscode_remote = False
    vscode_indicators = [
        'VSCODE_INJECTION',
        'VSCODE_IPC_HOOK',
        'VSCODE_IPC_HOOK_CLI',
        'TERM_PROGRAM',
    ]
    
    for var in vscode_indicators:
        value = os.getenv(var, '')
        if value and ('vscode' in value.lower() or 'code' in value.lower()):
            is_vscode_remote = True
            print(f"✅ VS Code 檢測: True (環境變數 {var}={value})")
            break
    
    cursor_indicators = [
        'CURSOR_USER_DATA_DIR',
        'CURSOR_LOGS_DIR',
    ]
    
    for var in cursor_indicators:
        if os.getenv(var):
            is_vscode_remote = True
            print(f"✅ Cursor 檢測: True (環境變數 {var})")
            break
    
    if not is_vscode_remote and os.getenv('VSCODE_INJECTION'):
        is_vscode_remote = True
        print(f"✅ VS Code 檢測: True (VSCODE_INJECTION)")
    
    if not is_vscode_remote:
        print(f"❌ VS Code/Cursor 檢測: False")
    
    print("\n=== 最終檢測結果 ===")
    print(f"平台: {sys.platform}")
    print(f"WSL 環境: {is_wsl}")
    print(f"SSH/遠端環境: {is_ssh_remote}")
    print(f"VS Code/Cursor 環境: {is_vscode_remote}")
    print(f"有 DISPLAY: {bool(os.getenv('DISPLAY'))}")
    
    print("\n=== 建議的啟動策略 ===")
    if is_vscode_remote:
        print("1. VS Code/Cursor Remote 策略")
        print("2. Cursor 專用策略")
    
    if is_ssh_remote:
        print("3. SSH 反向連接策略")
        print("4. SSH 端口轉發提示")
    
    if is_wsl:
        print("5. WSL Windows 瀏覽器策略")
    
    if not is_ssh_remote and not is_vscode_remote and not os.getenv('DISPLAY'):
        print("6. SSH 反向連接回退策略（環境變數可能未正確傳遞）")
    
    print("7. 本地瀏覽器策略")
    print("8. 系統預設策略")
    print("9. 手動回退策略")

if __name__ == "__main__":
    test_environment()
