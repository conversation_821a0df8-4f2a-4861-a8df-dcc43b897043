#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 SSH 客戶端 IP 檢測
"""

import os
import sys
import subprocess

def test_ip_detection():
    """測試 IP 檢測功能"""
    print("=== SSH 客戶端 IP 檢測測試 ===")
    
    # 檢查環境變數
    print("\n=== 環境變數檢查 ===")
    env_vars = ['SSH_CLIENT', 'SSH_CONNECTION', 'SSH_TTY', 'SSH_CLIENT_IP']
    for var in env_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: (未設定)")
    
    # 檢查網路連接
    print("\n=== 網路連接檢查 ===")
    try:
        result = subprocess.run(['netstat', '-tn'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ netstat 可用")
            lines = result.stdout.split('\n')
            ssh_connections = []
            for line in lines:
                if ':22 ' in line and 'ESTABLISHED' in line:
                    ssh_connections.append(line.strip())
            
            if ssh_connections:
                print(f"找到 {len(ssh_connections)} 個 SSH 連接:")
                for conn in ssh_connections:
                    print(f"  {conn}")
            else:
                print("❌ 沒有找到 SSH 連接")
        else:
            print("❌ netstat 執行失敗")
    except Exception as e:
        print(f"❌ netstat 檢查失敗: {e}")
    
    # 檢查 /proc/net/tcp
    print("\n=== /proc/net/tcp 檢查 ===")
    try:
        with open('/proc/net/tcp', 'r') as f:
            lines = f.readlines()
            ssh_lines = []
            for line in lines[1:]:  # 跳過標題行
                if ':0016' in line:  # 端口 22
                    ssh_lines.append(line.strip())
            
            if ssh_lines:
                print(f"找到 {len(ssh_lines)} 個端口 22 的連接:")
                for line in ssh_lines:
                    print(f"  {line}")
            else:
                print("❌ 沒有找到端口 22 的連接")
    except Exception as e:
        print(f"❌ /proc/net/tcp 檢查失敗: {e}")
    
    # 測試本機 IP
    print("\n=== 本機 IP 檢查 ===")
    try:
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('*******', 80))
        local_ip = s.getsockname()[0]
        s.close()
        print(f"✅ 本機 IP: {local_ip}")
        
        # 推測客戶端 IP
        ip_parts = local_ip.split('.')
        if len(ip_parts) == 4:
            possible_ips = [
                f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}.1",
                f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}.254",
                f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}.237",  # 用戶的 IP
            ]
            print(f"推測的客戶端 IP: {possible_ips}")
    except Exception as e:
        print(f"❌ 本機 IP 檢查失敗: {e}")
    
    # 測試通用瀏覽器啟動器
    print("\n=== 通用瀏覽器啟動器測試 ===")
    try:
        sys.path.insert(0, 'src')
        from mcp_feedback_enhanced.web.utils.universal_browser import get_universal_launcher
        
        launcher = get_universal_launcher()
        env = launcher.environment
        
        print(f"檢測到的客戶端 IP: {env.get('ssh_client_ip', 'None')}")
        print(f"是否為 SSH remote: {env.get('is_ssh_remote', False)}")
        
    except Exception as e:
        print(f"❌ 通用瀏覽器啟動器測試失敗: {e}")
    
    print("\n=== 建議 ===")
    print("如果檢測到的客戶端 IP 不正確，請設定環境變數：")
    print("export SSH_CLIENT_IP=**************")
    print("然後重新執行測試")

if __name__ == "__main__":
    test_ip_detection()
