#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用瀏覽器啟動器
================

支援各種環境下的瀏覽器啟動，包括：
- 本地環境（Windows、macOS、Linux）
- SSH Remote 環境
- WSL 環境
- VS Code Remote 環境
- Docker 容器環境

作者: Minidoracat
"""

import os
import sys
import subprocess
import webbrowser
import socket
import json
import time
from typing import Optional, List, Dict, Any
from urllib.parse import urlparse

# 導入調試功能
from ...debug import server_debug_log as debug_log

# 導入配置管理
from .browser_config import get_browser_config


class UniversalBrowserLauncher:
    """通用瀏覽器啟動器"""

    def __init__(self):
        self.config = get_browser_config()
        self.environment = self._detect_environment()
        self.strategies = self._get_launch_strategies()
        
    def _detect_environment(self) -> Dict[str, Any]:
        """檢測當前運行環境"""
        env = {
            'platform': sys.platform,
            'is_wsl': self._is_wsl(),
            'is_ssh_remote': self._is_ssh_remote(),
            'is_vscode_remote': self._is_vscode_remote(),
            'is_docker': self._is_docker(),
            'has_display': bool(os.getenv('DISPLAY')),
            'ssh_client_ip': self._get_ssh_client_ip(),
            'available_browsers': self._find_available_browsers(),
        }
        
        debug_log(f"環境檢測結果: {env}")
        return env
    
    def _is_wsl(self) -> bool:
        """檢測是否為 WSL 環境"""
        try:
            if os.path.exists('/proc/version'):
                with open('/proc/version', 'r') as f:
                    version_info = f.read().lower()
                    if 'microsoft' in version_info or 'wsl' in version_info:
                        return True
            
            wsl_env_vars = ['WSL_DISTRO_NAME', 'WSL_INTEROP', 'WSLENV']
            for var in wsl_env_vars:
                if os.getenv(var):
                    return True
                    
            wsl_paths = ['/mnt/c', '/mnt/d', '/proc/sys/fs/binfmt_misc/WSLInterop']
            for path in wsl_paths:
                if os.path.exists(path):
                    return True
        except:
            pass
        return False
    
    def _is_ssh_remote(self) -> bool:
        """檢測是否為 SSH 遠端環境"""
        # 檢查標準 SSH 環境變數
        ssh_vars = ['SSH_CONNECTION', 'SSH_CLIENT', 'SSH_TTY']
        if any(os.getenv(var) for var in ssh_vars):
            return True

        # 檢查其他遠端環境指標
        remote_indicators = [
            'REMOTE_CONTAINERS',  # VS Code Remote Containers
            'CODESPACES',         # GitHub Codespaces
            'GITPOD_WORKSPACE_ID', # Gitpod
        ]
        if any(os.getenv(var) for var in remote_indicators):
            return True

        # 檢查是否在無 DISPLAY 的 Linux 環境（通常是遠端）
        if (sys.platform.startswith('linux') and
            not os.getenv('DISPLAY') and
            not self._is_wsl()):
            return True

        # 檢查 Docker 容器
        if os.path.exists('/.dockerenv'):
            return True

        return False
    
    def _is_vscode_remote(self) -> bool:
        """檢測是否為 VS Code Remote 環境"""
        vscode_indicators = [
            'VSCODE_INJECTION',
            'VSCODE_IPC_HOOK',
            'VSCODE_IPC_HOOK_CLI',
            'TERM_PROGRAM',  # 可能是 'vscode'
        ]

        # 檢查 VS Code 相關環境變數
        for var in vscode_indicators:
            value = os.getenv(var, '')
            if value and ('vscode' in value.lower() or 'code' in value.lower()):
                return True

        # 檢查 Cursor 環境（Cursor 是基於 VS Code 的）
        cursor_indicators = [
            'CURSOR_USER_DATA_DIR',
            'CURSOR_LOGS_DIR',
        ]

        for var in cursor_indicators:
            if os.getenv(var):
                debug_log(f"檢測到 Cursor 環境: {var}")
                return True

        return bool(os.getenv('VSCODE_INJECTION'))
    
    def _is_docker(self) -> bool:
        """檢測是否為 Docker 容器環境"""
        return os.path.exists('/.dockerenv')
    
    def _get_ssh_client_ip(self) -> Optional[str]:
        """獲取 SSH 客戶端 IP"""
        # 檢查標準 SSH 環境變數
        ssh_client = os.getenv('SSH_CLIENT')
        if ssh_client:
            try:
                ip = ssh_client.split()[0]
                debug_log(f"從 SSH_CLIENT 獲取客戶端 IP: {ip}")
                return ip
            except:
                pass

        ssh_connection = os.getenv('SSH_CONNECTION')
        if ssh_connection:
            try:
                ip = ssh_connection.split()[0]
                debug_log(f"從 SSH_CONNECTION 獲取客戶端 IP: {ip}")
                return ip
            except:
                pass

        # 嘗試從網路連接推測
        try:
            import socket
            # 嘗試連接到常見的外部服務來獲取本機 IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(('*******', 80))
            local_ip = s.getsockname()[0]
            s.close()

            # 推測客戶端可能在同一網段
            ip_parts = local_ip.split('.')
            if len(ip_parts) == 4:
                # 嘗試常見的客戶端 IP（通常是網關或 .1）
                possible_ips = [
                    f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}.1",
                    f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}.254",
                ]
                debug_log(f"推測可能的客戶端 IP: {possible_ips}")
                return possible_ips[0]  # 返回最可能的一個
        except:
            pass

        debug_log("無法獲取 SSH 客戶端 IP")
        return None
    
    def _find_available_browsers(self) -> List[str]:
        """查找可用的瀏覽器"""
        browsers = []
        
        # 常見瀏覽器命令
        browser_commands = [
            'google-chrome', 'chrome', 'chromium', 'chromium-browser',
            'firefox', 'firefox-esr',
            'safari', 'edge', 'opera',
            'xdg-open',  # Linux 通用
            'open',      # macOS 通用
            'start',     # Windows 通用
        ]
        
        for cmd in browser_commands:
            try:
                result = subprocess.run(['which', cmd], capture_output=True, timeout=2)
                if result.returncode == 0:
                    browsers.append(cmd)
            except:
                pass
        
        return browsers
    
    def _get_launch_strategies(self) -> List[callable]:
        """獲取瀏覽器啟動策略列表"""
        strategies = []

        # VS Code/Cursor Remote 優先
        if self.environment['is_vscode_remote']:
            strategies.extend([
                self._launch_vscode_remote,
                self._launch_cursor_remote,
            ])

        # SSH Remote 策略
        if self.environment['is_ssh_remote']:
            strategies.extend([
                self._launch_ssh_reverse,
                self._launch_ssh_port_forward_hint,
            ])

        # WSL 策略
        if self.environment['is_wsl']:
            strategies.extend([
                self._launch_wsl_windows,
                self._launch_wsl_fallback,
            ])

        # 如果沒有檢測到明確的遠端環境，但沒有 DISPLAY，
        # 仍然嘗試 SSH 策略（可能是環境變數沒有正確傳遞）
        if (not self.environment['is_ssh_remote'] and
            not self.environment['is_vscode_remote'] and
            not self.environment['has_display'] and
            sys.platform.startswith('linux')):
            debug_log("未檢測到明確的遠端環境，但無 DISPLAY，嘗試 SSH 策略")
            strategies.extend([
                self._launch_ssh_reverse_fallback,
                self._launch_ssh_port_forward_hint,
            ])

        # 本地啟動策略
        strategies.extend([
            self._launch_local_browser,
            self._launch_system_default,
            self._launch_experimental_methods,
            self._launch_manual_fallback,
        ])

        return strategies
    
    def open_browser(self, url: str, single_attempt: bool = None) -> bool:
        """
        開啟瀏覽器

        Args:
            url: 要開啟的 URL
            single_attempt: 是否只嘗試一次（避免重複啟動）

        Returns:
            bool: 是否成功啟動瀏覽器
        """
        debug_log(f"嘗試開啟瀏覽器: {url}")
        debug_log(f"當前環境: {self.environment}")

        # 檢查配置
        if single_attempt is None:
            single_attempt = self.config.is_single_attempt()

        success_count = 0
        max_attempts = 1 if single_attempt else len(self.strategies)

        # 嘗試各種策略
        debug_log(f"共有 {len(self.strategies)} 個策略，單次嘗試模式: {single_attempt}")

        for i, strategy in enumerate(self.strategies):
            if success_count > 0 and single_attempt:
                debug_log(f"已成功啟動瀏覽器 ({success_count} 次)，跳過剩餘策略")
                break

            try:
                debug_log(f"🔄 嘗試策略 {i+1}/{len(self.strategies)}: {strategy.__name__}")
                strategy_result = strategy(url)
                if strategy_result:
                    debug_log(f"✅ 策略 {strategy.__name__} 回報成功")
                    success_count += 1
                    if single_attempt:
                        debug_log(f"單次嘗試模式，立即返回成功")
                        return True
                else:
                    debug_log(f"❌ 策略 {strategy.__name__} 回報失敗")
            except Exception as e:
                debug_log(f"💥 策略 {strategy.__name__} 發生異常: {e}")
                import traceback
                debug_log(f"異常詳情: {traceback.format_exc()}")

            # 在單次嘗試模式下，如果已經有成功的策略就停止
            if single_attempt and success_count > 0:
                debug_log(f"單次嘗試模式，已有 {success_count} 個成功策略，停止嘗試")
                break

            debug_log(f"策略 {i+1} 完成，繼續下一個策略...")
            debug_log("-" * 50)

        if success_count > 0:
            debug_log(f"✅ 共有 {success_count} 個策略成功啟動瀏覽器")
            return True
        else:
            debug_log("❌ 所有瀏覽器啟動策略都失敗")
            return False
    
    def _launch_vscode_remote(self, url: str) -> bool:
        """VS Code Remote 環境啟動策略"""
        try:
            # VS Code 會自動處理端口轉發，直接使用標準方法
            webbrowser.open(url)
            return True
        except:
            return False

    def _launch_cursor_remote(self, url: str) -> bool:
        """Cursor Remote 環境啟動策略"""
        try:
            # Cursor 基於 VS Code，但可能需要特殊處理
            debug_log("嘗試 Cursor 專用瀏覽器啟動")

            # 嘗試多種方法
            methods = [
                lambda: webbrowser.open(url),
                lambda: subprocess.run(['xdg-open', url], capture_output=True, timeout=5),
                lambda: subprocess.run(['google-chrome', url], capture_output=True, timeout=5),
            ]

            for method in methods:
                try:
                    result = method()
                    if hasattr(result, 'returncode'):
                        if result.returncode == 0:
                            debug_log("Cursor 瀏覽器啟動成功")
                            return True
                    else:
                        debug_log("Cursor webbrowser.open 執行完成")
                        return True
                except Exception as e:
                    debug_log(f"Cursor 方法失敗: {e}")
                    continue

            return False
        except Exception as e:
            debug_log(f"Cursor 瀏覽器啟動異常: {e}")
            return False
    
    def _launch_ssh_reverse(self, url: str) -> bool:
        """SSH 反向連接啟動策略"""
        client_ip = self.environment['ssh_client_ip']
        if not client_ip:
            debug_log("沒有 SSH 客戶端 IP，跳過 SSH 反向連接")
            return False

        debug_log(f"嘗試 SSH 反向連接到客戶端: {client_ip}")

        # 替換 URL 中的 localhost 為實際 IP
        parsed = urlparse(url)
        original_url = url
        if parsed.hostname in ['localhost', '127.0.0.1']:
            # 獲取本機 IP
            try:
                s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                s.connect((client_ip, 80))
                local_ip = s.getsockname()[0]
                s.close()
                url = url.replace(parsed.netloc, f"{local_ip}:{parsed.port}")
                debug_log(f"URL 替換: {original_url} -> {url}")
            except Exception as e:
                debug_log(f"無法獲取本機 IP: {e}")

        # 嘗試在客戶端執行瀏覽器命令
        commands = [
            f'open "{url}"',                    # macOS
            f'start "" "{url}"',                # Windows (更安全的語法)
            f'cmd /c start "" "{url}"',         # Windows 替代方法
            f'powershell -c "Start-Process \\"{url}\\""',  # Windows PowerShell
            f'xdg-open "{url}"',                # Linux
            f'google-chrome "{url}"',           # Linux Chrome
            f'firefox "{url}"',                 # Linux Firefox
        ]

        for cmd in commands:
            try:
                # 使用更寬鬆的 SSH 選項
                ssh_options = [
                    '-o ConnectTimeout=3',
                    '-o StrictHostKeyChecking=no',
                    '-o UserKnownHostsFile=/dev/null',
                    '-o PasswordAuthentication=no',
                    '-o BatchMode=yes',
                ]
                full_cmd = f'ssh {" ".join(ssh_options)} {client_ip} "{cmd}"'
                debug_log(f"執行 SSH 命令: {full_cmd}")

                result = subprocess.run(full_cmd, shell=True, capture_output=True, timeout=8)
                debug_log(f"SSH 命令結果: 返回碼={result.returncode}, stdout={result.stdout.decode()[:100]}, stderr={result.stderr.decode()[:100]}")

                if result.returncode == 0:
                    debug_log(f"✅ SSH 反向連接成功: {cmd}")
                    return True
                else:
                    debug_log(f"❌ SSH 命令失敗: {cmd}")
            except Exception as e:
                debug_log(f"❌ SSH 命令異常: {cmd}, 錯誤: {e}")
                continue

        debug_log("所有 SSH 反向連接嘗試都失敗")
        return False

    def _launch_ssh_reverse_fallback(self, url: str) -> bool:
        """SSH 反向連接回退策略（嘗試常見的客戶端 IP）"""
        # 嘗試常見的客戶端 IP 範圍
        common_client_ips = [
            '***********',    # 常見路由器 IP
            '***********',    # 常見路由器 IP
            '********',       # 常見內網 IP
            '**********',     # 常見內網 IP
        ]

        # 嘗試從網路連接中推測客戶端 IP
        try:
            import socket
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            # 推測可能的客戶端 IP（通常是同網段的 .1）
            ip_parts = local_ip.split('.')
            if len(ip_parts) == 4:
                gateway_ip = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}.1"
                common_client_ips.insert(0, gateway_ip)
        except:
            pass

        # 替換 URL 中的 localhost 為本機 IP
        parsed = urlparse(url)
        if parsed.hostname in ['localhost', '127.0.0.1']:
            try:
                s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                s.connect(('*******', 80))
                local_ip = s.getsockname()[0]
                s.close()
                url = url.replace(parsed.netloc, f"{local_ip}:{parsed.port}")
            except:
                pass

        # 嘗試在可能的客戶端執行瀏覽器命令
        commands = [
            f'open "{url}"',      # macOS
            f'start "{url}"',     # Windows
            f'xdg-open "{url}"',  # Linux
        ]

        for client_ip in common_client_ips:
            for cmd in commands:
                try:
                    full_cmd = f'ssh -o ConnectTimeout=1 -o StrictHostKeyChecking=no -o PasswordAuthentication=no {client_ip} "{cmd}"'
                    result = subprocess.run(full_cmd, shell=True, capture_output=True, timeout=3)
                    if result.returncode == 0:
                        debug_log(f"SSH 反向連接回退成功: {client_ip} - {cmd}")
                        return True
                except:
                    continue

        return False

    def _launch_ssh_port_forward_hint(self, url: str) -> bool:
        """SSH 端口轉發提示策略"""
        parsed = urlparse(url)
        port = parsed.port or 80
        client_ip = self.environment.get('ssh_client_ip', 'YOUR_CLIENT_IP')

        debug_log("=" * 60)
        debug_log("🌐 SSH Remote 環境檢測到")
        debug_log("💡 建議使用以下方式在本地開啟瀏覽器：")
        debug_log("")
        debug_log("方法 1: SSH 端口轉發（推薦）")
        debug_log(f"   在本地終端執行: ssh -L {port}:localhost:{port} user@{client_ip}")
        debug_log(f"   然後在本地瀏覽器開啟: http://localhost:{port}")
        debug_log("")
        debug_log("方法 2: 直接訪問（如果防火牆允許）")
        debug_log(f"   在本地瀏覽器開啟: {url.replace('127.0.0.1', client_ip).replace('localhost', client_ip)}")
        debug_log("")
        debug_log("方法 3: 手動複製 URL")
        debug_log(f"   URL: {url}")
        debug_log("=" * 60)

        return False  # 這只是提示，不算成功
    
    def _launch_wsl_windows(self, url: str) -> bool:
        """WSL 環境啟動 Windows 瀏覽器"""
        commands = [
            ['cmd.exe', '/c', 'start', url],
            ['powershell.exe', '-c', f'Start-Process "{url}"'],
            ['wslview', url],
        ]
        
        for cmd in commands:
            try:
                result = subprocess.run(cmd, capture_output=True, timeout=10)
                if result.returncode == 0:
                    return True
            except:
                continue
        
        return False
    
    def _launch_wsl_fallback(self, url: str) -> bool:
        """WSL 環境回退策略"""
        try:
            webbrowser.open(url)
            return True
        except:
            return False
    
    def _launch_local_browser(self, url: str) -> bool:
        """本地瀏覽器啟動策略"""
        try:
            # 在 SSH remote 環境下，本地瀏覽器啟動通常無效
            if self.environment['is_ssh_remote']:
                debug_log("SSH remote 環境，本地瀏覽器啟動通常無效，但仍嘗試")

                # 嘗試直接命令，但不期望成功
                commands = [
                    ['xdg-open', url],
                    ['google-chrome', '--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu', '--remote-debugging-port=0', url],
                    ['chromium-browser', '--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu', url],
                    ['firefox', '--new-tab', url],
                ]

                any_success = False
                for cmd in commands:
                    try:
                        debug_log(f"嘗試命令: {' '.join(cmd)}")
                        result = subprocess.run(cmd, capture_output=True, timeout=5)
                        if result.returncode == 0:
                            debug_log(f"命令執行成功: {cmd[0]}")
                            any_success = True
                            break
                        else:
                            debug_log(f"命令執行失敗: {cmd[0]}, 返回碼: {result.returncode}")
                    except FileNotFoundError:
                        debug_log(f"命令不存在: {cmd[0]}")
                        continue
                    except Exception as e:
                        debug_log(f"命令執行異常: {cmd[0]}, 錯誤: {e}")
                        continue

                if not any_success:
                    # 所有直接命令都失敗，嘗試 webbrowser（但不期望真正成功）
                    debug_log("直接命令都失敗，嘗試 webbrowser.open（可能無效）")
                    webbrowser.open(url)

                # 在 SSH 環境下，即使命令"成功"執行，也可能沒有真正啟動瀏覽器
                # 我們返回 False，讓後續策略提供手動操作指引
                debug_log("SSH remote 環境下的本地瀏覽器啟動可能無效")
                return False
            else:
                # 本地環境，直接使用 webbrowser
                webbrowser.open(url)
                return True
        except Exception as e:
            debug_log(f"本地瀏覽器啟動異常: {e}")
            return False
    
    def _launch_system_default(self, url: str) -> bool:
        """系統預設啟動策略"""
        commands = []
        
        if sys.platform == 'darwin':  # macOS
            commands = [['open', url]]
        elif sys.platform == 'win32':  # Windows
            commands = [['start', url]]
        else:  # Linux
            commands = [['xdg-open', url]]
        
        for cmd in commands:
            try:
                result = subprocess.run(cmd, capture_output=True, timeout=10)
                if result.returncode == 0:
                    return True
            except:
                continue
        
        return False

    def _launch_experimental_methods(self, url: str) -> bool:
        """實驗性瀏覽器啟動方法"""
        debug_log("🧪 嘗試實驗性瀏覽器啟動方法")

        methods = []

        # 方法 1: 嘗試使用 Python 的 http.server 和重定向
        methods.append(("HTTP 重定向", self._try_http_redirect, url))

        # 方法 2: 嘗試寫入臨時 HTML 文件並開啟
        methods.append(("臨時 HTML 文件", self._try_temp_html, url))

        # 方法 3: 嘗試使用 curl 觸發本地瀏覽器
        methods.append(("curl 觸發", self._try_curl_trigger, url))

        # 方法 4: 嘗試使用 netcat 發送 HTTP 請求
        methods.append(("netcat 觸發", self._try_netcat_trigger, url))

        success_count = 0
        for name, method, arg in methods:
            try:
                debug_log(f"🧪 實驗方法: {name}")
                if method(arg):
                    debug_log(f"✅ 實驗方法 {name} 成功")
                    success_count += 1
                else:
                    debug_log(f"❌ 實驗方法 {name} 失敗")
            except Exception as e:
                debug_log(f"💥 實驗方法 {name} 異常: {e}")

        return success_count > 0

    def _try_http_redirect(self, url: str) -> bool:
        """嘗試 HTTP 重定向方法"""
        try:
            # 創建一個簡單的重定向頁面
            redirect_html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta http-equiv="refresh" content="0; url={url}">
                <title>Redirecting...</title>
            </head>
            <body>
                <p>Redirecting to <a href="{url}">{url}</a></p>
                <script>window.location.href = "{url}";</script>
            </body>
            </html>
            """

            # 寫入臨時文件
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
                f.write(redirect_html)
                temp_file = f.name

            # 嘗試開啟臨時文件
            commands = [
                ['xdg-open', temp_file],
                ['google-chrome', temp_file],
                ['firefox', temp_file],
            ]

            for cmd in commands:
                try:
                    result = subprocess.run(cmd, capture_output=True, timeout=3)
                    if result.returncode == 0:
                        debug_log(f"HTTP 重定向成功: {cmd[0]}")
                        return True
                except:
                    continue

            # 清理臨時文件
            try:
                os.unlink(temp_file)
            except:
                pass

        except Exception as e:
            debug_log(f"HTTP 重定向方法異常: {e}")

        return False

    def _try_temp_html(self, url: str) -> bool:
        """嘗試臨時 HTML 文件方法"""
        try:
            # 創建一個包含 JavaScript 自動跳轉的頁面
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>MCP Feedback Enhanced</title>
                <script>
                    setTimeout(function() {{
                        window.open('{url}', '_blank');
                    }}, 1000);
                </script>
            </head>
            <body>
                <h1>MCP Feedback Enhanced</h1>
                <p>正在啟動瀏覽器...</p>
                <p><a href="{url}" target="_blank">點擊這裡手動開啟</a></p>
            </body>
            </html>
            """

            # 寫入到用戶主目錄
            import os
            home_dir = os.path.expanduser("~")
            html_file = os.path.join(home_dir, "mcp_feedback.html")

            with open(html_file, 'w') as f:
                f.write(html_content)

            debug_log(f"創建臨時 HTML 文件: {html_file}")

            # 嘗試開啟
            result = subprocess.run(['xdg-open', html_file], capture_output=True, timeout=3)
            return result.returncode == 0

        except Exception as e:
            debug_log(f"臨時 HTML 文件方法異常: {e}")

        return False

    def _try_curl_trigger(self, url: str) -> bool:
        """嘗試使用 curl 觸發"""
        try:
            # 嘗試向本地發送請求，可能觸發某些瀏覽器行為
            result = subprocess.run(['curl', '-s', '-o', '/dev/null', url],
                                  capture_output=True, timeout=5)
            debug_log(f"curl 觸發結果: {result.returncode}")
            return result.returncode == 0
        except Exception as e:
            debug_log(f"curl 觸發異常: {e}")

        return False

    def _try_netcat_trigger(self, url: str) -> bool:
        """嘗試使用 netcat 觸發"""
        try:
            parsed = urlparse(url)
            port = parsed.port or 80

            # 嘗試連接到本地端口
            result = subprocess.run(['nc', '-z', 'localhost', str(port)],
                                  capture_output=True, timeout=3)
            debug_log(f"netcat 觸發結果: {result.returncode}")
            return result.returncode == 0
        except Exception as e:
            debug_log(f"netcat 觸發異常: {e}")

        return False

    def _launch_manual_fallback(self, url: str) -> bool:
        """手動啟動回退策略"""
        client_ip = self.environment.get('ssh_client_ip', 'YOUR_CLIENT_IP')
        parsed = urlparse(url)
        port = parsed.port or 80

        debug_log("=" * 70)
        debug_log("🌐 無法自動啟動瀏覽器")
        debug_log("📋 請使用以下方法之一在本地開啟瀏覽器：")
        debug_log("")

        if self.environment['is_ssh_remote']:
            debug_log("🔗 方法 1: SSH 端口轉發（推薦）")
            debug_log(f"   1. 在本地終端執行: ssh -L {port}:localhost:{port} user@{client_ip}")
            debug_log(f"   2. 在本地瀏覽器開啟: http://localhost:{port}")
            debug_log("")

            debug_log("🌍 方法 2: 直接訪問（如果防火牆允許）")
            direct_url = url.replace('127.0.0.1', client_ip).replace('localhost', client_ip)
            debug_log(f"   在本地瀏覽器開啟: {direct_url}")
            debug_log("")

            debug_log("📋 方法 3: 手動複製 URL")
            debug_log(f"   原始 URL: {url}")
            debug_log("")

            debug_log("💡 提示：")
            debug_log("   - 如果使用 VS Code/Cursor，它可能會自動處理端口轉發")
            debug_log("   - 檢查編輯器的端口轉發面板或通知")
            debug_log("   - 確保遠端服務器的防火牆允許相應端口")
        else:
            debug_log("📋 請手動複製以下 URL 到瀏覽器：")
            debug_log(f"   {url}")

        debug_log("=" * 70)

        return False  # 這只是提示，不算成功


# 全域實例
_launcher = None

def get_universal_launcher() -> UniversalBrowserLauncher:
    """獲取通用瀏覽器啟動器實例"""
    global _launcher
    if _launcher is None:
        _launcher = UniversalBrowserLauncher()
    return _launcher

def universal_browser_open(url: str, single_attempt: bool = None) -> bool:
    """
    通用瀏覽器開啟函數

    Args:
        url: 要開啟的 URL
        single_attempt: 是否只嘗試一次（預設從配置讀取，通常為 True）

    Returns:
        bool: 是否成功啟動瀏覽器
    """
    launcher = get_universal_launcher()
    return launcher.open_browser(url, single_attempt)
