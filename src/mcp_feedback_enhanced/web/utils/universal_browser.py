#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用瀏覽器啟動器
================

支援各種環境下的瀏覽器啟動，包括：
- 本地環境（Windows、macOS、Linux）
- SSH Remote 環境
- WSL 環境
- VS Code Remote 環境
- Docker 容器環境

作者: Minidoracat
"""

import os
import sys
import subprocess
import webbrowser
import socket
import json
import time
from typing import Optional, List, Dict, Any
from urllib.parse import urlparse

# 導入調試功能
from ...debug import server_debug_log as debug_log

# 導入配置管理
from .browser_config import get_browser_config


class UniversalBrowserLauncher:
    """通用瀏覽器啟動器"""

    def __init__(self):
        self.config = get_browser_config()
        self.environment = self._detect_environment()
        self.strategies = self._get_launch_strategies()
        
    def _detect_environment(self) -> Dict[str, Any]:
        """檢測當前運行環境"""
        env = {
            'platform': sys.platform,
            'is_wsl': self._is_wsl(),
            'is_ssh_remote': self._is_ssh_remote(),
            'is_vscode_remote': self._is_vscode_remote(),
            'is_docker': self._is_docker(),
            'has_display': bool(os.getenv('DISPLAY')),
            'ssh_client_ip': self._get_ssh_client_ip(),
            'available_browsers': self._find_available_browsers(),
        }
        
        debug_log(f"環境檢測結果: {env}")
        return env
    
    def _is_wsl(self) -> bool:
        """檢測是否為 WSL 環境"""
        try:
            if os.path.exists('/proc/version'):
                with open('/proc/version', 'r') as f:
                    version_info = f.read().lower()
                    if 'microsoft' in version_info or 'wsl' in version_info:
                        return True
            
            wsl_env_vars = ['WSL_DISTRO_NAME', 'WSL_INTEROP', 'WSLENV']
            for var in wsl_env_vars:
                if os.getenv(var):
                    return True
                    
            wsl_paths = ['/mnt/c', '/mnt/d', '/proc/sys/fs/binfmt_misc/WSLInterop']
            for path in wsl_paths:
                if os.path.exists(path):
                    return True
        except:
            pass
        return False
    
    def _is_ssh_remote(self) -> bool:
        """檢測是否為 SSH 遠端環境"""
        ssh_vars = ['SSH_CONNECTION', 'SSH_CLIENT', 'SSH_TTY']
        return any(os.getenv(var) for var in ssh_vars)
    
    def _is_vscode_remote(self) -> bool:
        """檢測是否為 VS Code Remote 環境"""
        return bool(os.getenv('VSCODE_INJECTION'))
    
    def _is_docker(self) -> bool:
        """檢測是否為 Docker 容器環境"""
        return os.path.exists('/.dockerenv')
    
    def _get_ssh_client_ip(self) -> Optional[str]:
        """獲取 SSH 客戶端 IP"""
        ssh_client = os.getenv('SSH_CLIENT')
        if ssh_client:
            return ssh_client.split()[0]
        
        ssh_connection = os.getenv('SSH_CONNECTION')
        if ssh_connection:
            return ssh_connection.split()[0]
        
        return None
    
    def _find_available_browsers(self) -> List[str]:
        """查找可用的瀏覽器"""
        browsers = []
        
        # 常見瀏覽器命令
        browser_commands = [
            'google-chrome', 'chrome', 'chromium', 'chromium-browser',
            'firefox', 'firefox-esr',
            'safari', 'edge', 'opera',
            'xdg-open',  # Linux 通用
            'open',      # macOS 通用
            'start',     # Windows 通用
        ]
        
        for cmd in browser_commands:
            try:
                result = subprocess.run(['which', cmd], capture_output=True, timeout=2)
                if result.returncode == 0:
                    browsers.append(cmd)
            except:
                pass
        
        return browsers
    
    def _get_launch_strategies(self) -> List[callable]:
        """獲取瀏覽器啟動策略列表"""
        strategies = []
        
        # 根據環境選擇策略
        if self.environment['is_vscode_remote']:
            strategies.append(self._launch_vscode_remote)
        
        if self.environment['is_ssh_remote']:
            strategies.extend([
                self._launch_ssh_reverse,
                self._launch_ssh_port_forward_hint,
            ])
        
        if self.environment['is_wsl']:
            strategies.extend([
                self._launch_wsl_windows,
                self._launch_wsl_fallback,
            ])
        
        # 本地啟動策略
        strategies.extend([
            self._launch_local_browser,
            self._launch_system_default,
            self._launch_manual_fallback,
        ])
        
        return strategies
    
    def open_browser(self, url: str, single_attempt: bool = None) -> bool:
        """
        開啟瀏覽器

        Args:
            url: 要開啟的 URL
            single_attempt: 是否只嘗試一次（避免重複啟動）

        Returns:
            bool: 是否成功啟動瀏覽器
        """
        debug_log(f"嘗試開啟瀏覽器: {url}")
        debug_log(f"當前環境: {self.environment}")

        # 檢查配置
        if single_attempt is None:
            single_attempt = self.config.is_single_attempt()

        success_count = 0
        max_attempts = 1 if single_attempt else len(self.strategies)

        # 嘗試各種策略
        for i, strategy in enumerate(self.strategies):
            if success_count > 0 and single_attempt:
                debug_log(f"已成功啟動瀏覽器，跳過剩餘策略")
                break

            try:
                debug_log(f"嘗試策略 {i+1}: {strategy.__name__}")
                if strategy(url):
                    debug_log(f"✅ 策略 {strategy.__name__} 成功")
                    success_count += 1
                    if single_attempt:
                        return True
                else:
                    debug_log(f"❌ 策略 {strategy.__name__} 失敗")
            except Exception as e:
                debug_log(f"❌ 策略 {strategy.__name__} 異常: {e}")

        if success_count > 0:
            debug_log(f"✅ 共有 {success_count} 個策略成功啟動瀏覽器")
            return True
        else:
            debug_log("❌ 所有瀏覽器啟動策略都失敗")
            return False
    
    def _launch_vscode_remote(self, url: str) -> bool:
        """VS Code Remote 環境啟動策略"""
        try:
            # VS Code 會自動處理端口轉發，直接使用標準方法
            webbrowser.open(url)
            return True
        except:
            return False
    
    def _launch_ssh_reverse(self, url: str) -> bool:
        """SSH 反向連接啟動策略"""
        if not self.environment['ssh_client_ip']:
            return False
        
        client_ip = self.environment['ssh_client_ip']
        
        # 替換 URL 中的 localhost 為實際 IP
        parsed = urlparse(url)
        if parsed.hostname in ['localhost', '127.0.0.1']:
            # 獲取本機 IP
            try:
                s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                s.connect((client_ip, 80))
                local_ip = s.getsockname()[0]
                s.close()
                url = url.replace(parsed.netloc, f"{local_ip}:{parsed.port}")
            except:
                pass
        
        # 嘗試在客戶端執行瀏覽器命令
        commands = [
            f'open "{url}"',      # macOS
            f'start "{url}"',     # Windows
            f'xdg-open "{url}"',  # Linux
        ]
        
        for cmd in commands:
            try:
                full_cmd = f'ssh -o ConnectTimeout=2 -o StrictHostKeyChecking=no {client_ip} "{cmd}"'
                result = subprocess.run(full_cmd, shell=True, capture_output=True, timeout=5)
                if result.returncode == 0:
                    debug_log(f"SSH 反向連接成功: {cmd}")
                    return True
            except:
                continue
        
        return False
    
    def _launch_ssh_port_forward_hint(self, url: str) -> bool:
        """SSH 端口轉發提示策略"""
        parsed = urlparse(url)
        port = parsed.port or 80
        
        print("\n" + "="*60)
        print("🌐 SSH Remote 環境檢測到")
        print("💡 建議使用以下方式在本地開啟瀏覽器：")
        print(f"   1. 設置 SSH 端口轉發: ssh -L {port}:localhost:{port} user@host")
        print(f"   2. 在本地瀏覽器開啟: http://localhost:{port}")
        print("="*60 + "\n")
        
        return False  # 這只是提示，不算成功
    
    def _launch_wsl_windows(self, url: str) -> bool:
        """WSL 環境啟動 Windows 瀏覽器"""
        commands = [
            ['cmd.exe', '/c', 'start', url],
            ['powershell.exe', '-c', f'Start-Process "{url}"'],
            ['wslview', url],
        ]
        
        for cmd in commands:
            try:
                result = subprocess.run(cmd, capture_output=True, timeout=10)
                if result.returncode == 0:
                    return True
            except:
                continue
        
        return False
    
    def _launch_wsl_fallback(self, url: str) -> bool:
        """WSL 環境回退策略"""
        try:
            webbrowser.open(url)
            return True
        except:
            return False
    
    def _launch_local_browser(self, url: str) -> bool:
        """本地瀏覽器啟動策略"""
        try:
            webbrowser.open(url)
            return True
        except:
            return False
    
    def _launch_system_default(self, url: str) -> bool:
        """系統預設啟動策略"""
        commands = []
        
        if sys.platform == 'darwin':  # macOS
            commands = [['open', url]]
        elif sys.platform == 'win32':  # Windows
            commands = [['start', url]]
        else:  # Linux
            commands = [['xdg-open', url]]
        
        for cmd in commands:
            try:
                result = subprocess.run(cmd, capture_output=True, timeout=10)
                if result.returncode == 0:
                    return True
            except:
                continue
        
        return False
    
    def _launch_manual_fallback(self, url: str) -> bool:
        """手動啟動回退策略"""
        print("\n" + "="*60)
        print("🌐 自動啟動瀏覽器失敗")
        print("📋 請手動複製以下 URL 到瀏覽器：")
        print(f"   {url}")
        print("="*60 + "\n")
        
        return False  # 這只是提示，不算成功


# 全域實例
_launcher = None

def get_universal_launcher() -> UniversalBrowserLauncher:
    """獲取通用瀏覽器啟動器實例"""
    global _launcher
    if _launcher is None:
        _launcher = UniversalBrowserLauncher()
    return _launcher

def universal_browser_open(url: str, single_attempt: bool = True) -> bool:
    """
    通用瀏覽器開啟函數

    Args:
        url: 要開啟的 URL
        single_attempt: 是否只嘗試一次（預設為 True，避免重複啟動）

    Returns:
        bool: 是否成功啟動瀏覽器
    """
    launcher = get_universal_launcher()
    return launcher.open_browser(url, single_attempt)
