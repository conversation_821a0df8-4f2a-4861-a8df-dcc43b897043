#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瀏覽器配置管理
==============

提供瀏覽器啟動的配置選項和環境變數支援。
"""

import os
from typing import Dict, Any, Optional


class BrowserConfig:
    """瀏覽器配置管理器"""
    
    def __init__(self):
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """載入配置"""
        config = {
            # 基本設定 - 預設只嘗試到第一個有效策略
            'single_attempt': self._get_bool_env('BROWSER_SINGLE_ATTEMPT', True),
            'timeout': self._get_int_env('BROWSER_TIMEOUT', 10),
            'show_manual_instructions': self._get_bool_env('BROWSER_SHOW_MANUAL', True),
            
            # SSH 相關設定
            'ssh_reverse_enabled': self._get_bool_env('BROWSER_SSH_REVERSE', True),
            'ssh_timeout': self._get_int_env('BROWSER_SSH_TIMEOUT', 5),
            
            # WSL 相關設定
            'wsl_use_windows_browser': self._get_bool_env('BROWSER_WSL_WINDOWS', True),
            
            # 偏好的瀏覽器
            'preferred_browser': os.getenv('BROWSER_PREFERRED', ''),
            
            # 調試模式
            'debug_mode': self._get_bool_env('BROWSER_DEBUG', False),
        }
        
        return config
    
    def _get_bool_env(self, key: str, default: bool) -> bool:
        """獲取布林型環境變數"""
        value = os.getenv(key, '').lower()
        if value in ('true', '1', 'yes', 'on'):
            return True
        elif value in ('false', '0', 'no', 'off'):
            return False
        else:
            return default
    
    def _get_int_env(self, key: str, default: int) -> int:
        """獲取整數型環境變數"""
        try:
            return int(os.getenv(key, str(default)))
        except ValueError:
            return default
    
    def get(self, key: str, default: Any = None) -> Any:
        """獲取配置值"""
        return self.config.get(key, default)
    
    def is_single_attempt(self) -> bool:
        """是否只嘗試一次啟動"""
        return self.get('single_attempt', True)
    
    def get_timeout(self) -> int:
        """獲取超時時間"""
        return self.get('timeout', 10)
    
    def should_show_manual_instructions(self) -> bool:
        """是否顯示手動操作指引"""
        return self.get('show_manual_instructions', True)
    
    def is_ssh_reverse_enabled(self) -> bool:
        """是否啟用 SSH 反向連接"""
        return self.get('ssh_reverse_enabled', True)
    
    def get_ssh_timeout(self) -> int:
        """獲取 SSH 超時時間"""
        return self.get('ssh_timeout', 5)
    
    def should_use_wsl_windows_browser(self) -> bool:
        """WSL 環境是否使用 Windows 瀏覽器"""
        return self.get('wsl_use_windows_browser', True)
    
    def get_preferred_browser(self) -> str:
        """獲取偏好的瀏覽器"""
        return self.get('preferred_browser', '')
    
    def is_debug_mode(self) -> bool:
        """是否為調試模式"""
        return self.get('debug_mode', False)
    
    def print_config_help(self):
        """打印配置說明"""
        # 導入調試功能
        try:
            from ...debug import server_debug_log as debug_log
        except ImportError:
            # 如果無法導入，使用標準 print
            def debug_log(msg):
                print(msg)

        debug_log("")
        debug_log("=== 瀏覽器啟動器配置選項 ===")
        debug_log("")
        debug_log("環境變數配置：")
        debug_log("")
        debug_log("基本設定：")
        debug_log("  BROWSER_SINGLE_ATTEMPT=true/false    # 是否只嘗試一次啟動（預設：true）")
        debug_log("  BROWSER_TIMEOUT=10                   # 啟動超時時間（秒，預設：10）")
        debug_log("  BROWSER_SHOW_MANUAL=true/false       # 是否顯示手動操作指引（預設：true）")
        debug_log("")
        debug_log("SSH 相關：")
        debug_log("  BROWSER_SSH_REVERSE=true/false       # 是否啟用 SSH 反向連接（預設：true）")
        debug_log("  BROWSER_SSH_TIMEOUT=5                # SSH 連接超時時間（秒，預設：5）")
        debug_log("")
        debug_log("WSL 相關：")
        debug_log("  BROWSER_WSL_WINDOWS=true/false       # WSL 是否使用 Windows 瀏覽器（預設：true）")
        debug_log("")
        debug_log("其他：")
        debug_log("  BROWSER_PREFERRED=chrome             # 偏好的瀏覽器（可選）")
        debug_log("  BROWSER_DEBUG=true/false             # 調試模式（預設：false）")
        debug_log("")
        debug_log("使用範例：")
        debug_log("  export BROWSER_SINGLE_ATTEMPT=false  # 如果需要嘗試多種方法")
        debug_log("  export BROWSER_PREFERRED=google-chrome")
        debug_log("  uvx --with-editable . mcp-feedback-enhanced test --web")
        debug_log("")


# 全域配置實例
_config: Optional[BrowserConfig] = None


def get_browser_config() -> BrowserConfig:
    """獲取瀏覽器配置實例"""
    global _config
    if _config is None:
        _config = BrowserConfig()
    return _config


def print_browser_config_help():
    """打印瀏覽器配置說明"""
    config = get_browser_config()
    config.print_config_help()


if __name__ == "__main__":
    # 當直接執行此檔案時，顯示配置說明
    print_browser_config_help()
