{"app": {"title": "MCP Interactive Feedback System", "subtitle": "AI Assistant Interactive Feedback Platform", "projectDirectory": "Project Directory"}, "tabs": {"feedback": "💬 Feedback", "summary": "📋 AI Summary", "commands": "⚡ Commands", "command": "⚡ Commands", "settings": "⚙️ Settings", "combined": "📝 Combined Mode", "about": "ℹ️ About"}, "feedback": {"title": "💬 Provide Feedback", "description": "Please provide your feedback on the AI assistant's work. You can enter text feedback and upload related images.", "textLabel": "Text Feedback", "placeholder": "Please enter your feedback here...", "detailedPlaceholder": "Please enter your feedback here...\n\n💡 Tips:\n• Press Ctrl+Enter/Cmd+Enter (numpad supported) for quick submit\n• Press Ctrl+V/Cmd+V to paste clipboard images directly", "imageLabel": "Image Attachments (Optional)", "imageUploadText": "📎 Click to select images or drag and drop images here\nSupports PNG, JPG, JPEG, GIF, BMP, WebP formats", "submit": "✅ Submit <PERSON>", "uploading": "Uploading...", "dragdrop": "Drag and drop images here or click to upload", "selectfiles": "Select Files", "processing": "Processing...", "success": "<PERSON><PERSON><PERSON> submitted successfully!", "error": "Error submitting feedback", "shortcuts": {"submit": "Ctrl+Enter to submit (Cmd+Enter on Mac, numpad supported)", "clear": "Ctrl+Delete to clear (Cmd+Delete on Mac)", "paste": "Ctrl+V to paste images (Cmd+V on Mac)"}}, "summary": {"title": "📋 AI Work Summary", "description": "Below is the work summary completed by the AI assistant. Please review carefully and provide your feedback.", "placeholder": "AI work summary will be displayed here...", "empty": "No summary content available", "lastupdate": "Last updated", "refresh": "Refresh"}, "commands": {"title": "⚡ Command Execution", "description": "Execute commands here to verify results or collect more information. Commands will be executed in the project directory.", "inputLabel": "Command Input", "placeholder": "Enter command to execute...", "execute": "▶️ Execute", "runButton": "▶️ Execute", "clear": "Clear", "output": "Command Output", "outputLabel": "Command Output", "running": "Running...", "completed": "Completed", "error": "Execution Error", "history": "Command History"}, "command": {"title": "⚡ Command Execution", "description": "Execute commands here to verify results or collect more information. Commands will be executed in the project directory.", "inputLabel": "Command Input", "placeholder": "Enter command to execute...", "execute": "▶️ Execute", "runButton": "▶️ Execute", "clear": "Clear", "output": "Command Output", "outputLabel": "Command Output", "running": "Running...", "completed": "Completed", "error": "Execution Error", "history": "Command History"}, "combined": {"description": "Combined mode: AI summary and feedback input are on the same page for easy comparison.", "summaryTitle": "📋 AI Work Summary", "feedbackTitle": "💬 Provide Feedback"}, "settings": {"title": "⚙️ Settings", "description": "Adjust interface settings and preference options.", "language": "Language", "currentLanguage": "Current Language", "languageDesc": "Select interface display language", "interface": "Interface Settings", "layoutMode": "Interface Layout Mode", "layoutModeDesc": "Select how AI summary and feedback input are displayed", "separateMode": "Separate Mode", "separateModeDesc": "AI summary and feedback are in separate tabs", "combinedVertical": "Combined Mode (Vertical Layout)", "combinedVerticalDesc": "AI summary on top, feedback input below, both on the same page", "combinedHorizontal": "Combined Mode (Horizontal Layout)", "combinedHorizontalDesc": "AI summary on left, feedback input on right, expanding summary viewing area", "autoClose": "Auto Close Page", "autoCloseDesc": "Automatically close page after submitting feedback", "theme": "Theme", "notifications": "Notifications", "advanced": "Advanced Settings", "save": "Save Settings", "reset": "Reset Settings", "resetDesc": "Clear all saved settings and restore to default state", "resetConfirm": "Are you sure you want to reset all settings? This will clear all saved preferences.", "resetSuccess": "Settings have been reset to default values", "resetError": "Error occurred while resetting settings", "timeout": "Connection Timeout (seconds)", "autorefresh": "Auto Refresh", "debug": "Debug Mode"}, "languages": {"zh-TW": "繁體中文", "zh-CN": "简体中文", "en": "English"}, "themes": {"dark": "Dark", "light": "Light", "auto": "Auto"}, "status": {"connected": "Connected", "connecting": "Connecting...", "disconnected": "Disconnected", "reconnecting": "Reconnecting...", "error": "Connection Error"}, "notifications": {"feedback_sent": "<PERSON><PERSON><PERSON> sent", "command_executed": "Command executed", "settings_saved": "Setting<PERSON> saved", "connection_lost": "Connection lost", "connection_restored": "Connection restored"}, "errors": {"connection_failed": "Connection failed", "upload_failed": "Upload failed", "command_failed": "Command execution failed", "invalid_input": "Invalid input", "timeout": "Request timeout"}, "buttons": {"ok": "OK", "cancel": "❌ Cancel", "submit": "✅ Submit <PERSON>", "retry": "Retry", "close": "Close", "upload": "Upload", "download": "Download"}, "session": {"timeout": "⏰ Session has timed out, interface will close automatically", "timeoutWarning": "Session is about to timeout", "timeoutDescription": "Due to prolonged inactivity, the session has timed out. The interface will automatically close in 3 seconds.", "closing": "Closing..."}, "timeout": {"enable": "Auto Close", "enableTooltip": "When enabled, the interface will automatically close after the specified time", "duration": {"label": "Timeout Duration", "description": "Set the auto-close time (30 seconds - 2 hours)"}, "seconds": "seconds", "remaining": "Time Remaining", "expired": "⏰ Time expired, interface will close automatically", "autoCloseMessage": "Interface will automatically close in {seconds} seconds", "settings": {"title": "Timeout Settings", "description": "When enabled, the interface will automatically close after the specified time. The countdown timer will be displayed in the header area."}}, "dynamic": {"aiSummary": "Test Web UI Functionality\n\n🎯 **Test Items:**\n- Web UI server startup and operation\n- WebSocket real-time communication\n- Feedback submission functionality\n- Image upload and preview\n- Command execution functionality\n- Smart Ctrl+V image pasting\n- Multi-language interface functionality\n\n📋 **Test Steps:**\n1. Test image upload (drag-drop, file selection, clipboard)\n2. Press Ctrl+V in text box to test smart pasting\n3. Try switching languages (Traditional Chinese/Simplified Chinese/English)\n4. Test command execution functionality\n5. Submit feedback and images\n\nPlease test these features and provide feedback!", "terminalWelcome": "Welcome to Interactive Feedback Terminal\n========================================\nProject Directory: {sessionId}\nEnter commands and press Enter or click Execute button\nSupported commands: ls, dir, pwd, cat, type, etc.\n\n$ "}, "about": {"title": "ℹ️ About", "description": "A powerful MCP server that provides human-in-the-loop interactive feedback functionality for AI-assisted development tools. Supports both Qt GUI and Web UI interfaces, with rich features including image upload, command execution, multi-language support, and more.", "appInfo": "Application Information", "version": "Version", "projectLinks": "Project Links", "githubProject": "GitHub Project", "visitGithub": "Visit GitHub", "contact": "Contact & Support", "discordSupport": "Discord Support", "joinDiscord": "Join <PERSON>", "contactDescription": "For technical support, issue reports, or feature suggestions, please contact us through Discord community or GitHub Issues.", "thanks": "Thanks & Contributions", "thanksText": "Thanks to the original author <PERSON><PERSON><PERSON> (@fabiomlfer<PERSON>ira) for creating the original interactive-feedback-mcp project.\n\nThis enhanced version is developed and maintained by Minidoracat, greatly expanding the project functionality with GUI interface, image support, multi-language capabilities, and many other improvements.\n\nSpecial thanks to sanshao85's mcp-feedback-collector project for UI design inspiration.\n\nOpen source collaboration makes technology better!"}, "images": {"settings": {"title": "Image Settings", "sizeLimit": "Image Size Limit", "sizeLimitOptions": {"unlimited": "Unlimited", "1mb": "1MB", "3mb": "3MB", "5mb": "5MB"}, "base64Detail": "Base64 Compatibility Mode", "base64DetailHelp": "When enabled, includes full Base64 image data in text, improving compatibility with certain AI models", "base64Warning": "⚠️ Increases transmission size", "compatibilityHint": "💡 Images not recognized correctly?", "enableBase64Hint": "Try enabling Base64 compatibility mode"}, "sizeLimitExceeded": "Image {filename} size is {size}, exceeds {limit} limit!", "sizeLimitExceededAdvice": "Consider compressing the image with editing software before uploading, or adjust the image size limit settings."}}