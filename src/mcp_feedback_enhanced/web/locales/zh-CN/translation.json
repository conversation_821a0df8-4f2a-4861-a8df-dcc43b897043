{"app": {"title": "MCP 交互反馈系统", "subtitle": "AI 助手交互反馈平台", "projectDirectory": "项目目录"}, "tabs": {"feedback": "💬 反馈", "summary": "📋 AI 总结", "commands": "⚡ 命令", "command": "⚡ 命令", "settings": "⚙️ 设置", "combined": "📝 合并模式", "about": "ℹ️ 关于"}, "feedback": {"title": "💬 提供反馈", "description": "请提供您对 AI 工作成果的反馈意见。您可以输入文字反馈并上传相关图片。", "textLabel": "文字反馈", "placeholder": "请在这里输入您的反馈...", "detailedPlaceholder": "请在这里输入您的反馈...\n\n💡 小提示：\n• 按 Ctrl+Enter/Cmd+Enter (支持数字键盘) 可快速提交\n• 按 Ctrl+V/Cmd+V 可直接粘贴剪贴板图片", "imageLabel": "图片附件（可选）", "imageUploadText": "📎 点击选择图片或拖放图片到此处\n支持 PNG、JPG、JPEG、GIF、BMP、WebP 等格式", "submit": "✅ 提交反馈", "uploading": "上传中...", "dragdrop": "拖放图片到这里或点击上传", "selectfiles": "选择文件", "processing": "处理中...", "success": "反馈已成功提交！", "error": "提交反馈时发生错误", "shortcuts": {"submit": "Ctrl+Enter 提交 (Mac 用 Cmd+Enter，支持数字键盘)", "clear": "Ctrl+Delete 清除 (Mac 用 Cmd+Delete)", "paste": "Ctrl+V 粘贴图片 (Mac 用 Cmd+V)"}}, "summary": {"title": "📋 AI 工作摘要", "description": "以下是 AI 助手完成的工作摘要，请仔细查看并提供您的反馈意见。", "placeholder": "AI 工作摘要将在这里显示...", "empty": "目前没有摘要内容", "lastupdate": "最后更新", "refresh": "刷新"}, "commands": {"title": "⚡ 命令执行", "description": "在此执行命令来验证结果或收集更多信息。命令将在项目目录中执行。", "inputLabel": "命令输入", "placeholder": "输入要执行的命令...", "execute": "▶️ 执行", "runButton": "▶️ 执行", "clear": "清除", "output": "命令输出", "outputLabel": "命令输出", "running": "执行中...", "completed": "执行完成", "error": "执行错误", "history": "命令历史"}, "command": {"title": "⚡ 命令执行", "description": "在此执行命令来验证结果或收集更多信息。命令将在项目目录中执行。", "inputLabel": "命令输入", "placeholder": "输入要执行的命令...", "execute": "▶️ 执行", "runButton": "▶️ 执行", "clear": "清除", "output": "命令输出", "outputLabel": "命令输出", "running": "执行中...", "completed": "执行完成", "error": "执行错误", "history": "命令历史"}, "combined": {"description": "合并模式：AI 摘要和反馈输入在同一页面中，方便对照查看。", "summaryTitle": "📋 AI 工作摘要", "feedbackTitle": "💬 提供反馈"}, "settings": {"title": "⚙️ 设定", "description": "调整界面设定和偏好选项。", "language": "语言", "currentLanguage": "当前语言", "languageDesc": "选择界面显示语言", "interface": "界面设定", "layoutMode": "界面布局模式", "layoutModeDesc": "选择 AI 摘要和反馈输入的显示方式", "separateMode": "分离模式", "separateModeDesc": "AI 摘要和反馈分别在不同页签", "combinedVertical": "合并模式（垂直布局）", "combinedVerticalDesc": "AI 摘要在上，反馈输入在下，摘要和反馈在同一页面", "combinedHorizontal": "合并模式（水平布局）", "combinedHorizontalDesc": "AI 摘要在左，反馈输入在右，增大摘要可视区域", "autoClose": "自动关闭页面", "autoCloseDesc": "提交回馈后自动关闭页面", "theme": "主题", "notifications": "通知", "advanced": "进阶设定", "save": "储存设定", "reset": "重置设定", "resetDesc": "清除所有已保存的设定，恢复到预设状态", "resetConfirm": "确定要重置所有设定吗？这将清除所有已保存的偏好设定。", "resetSuccess": "设定已重置为预设值", "resetError": "重置设定时发生错误", "timeout": "连线逾时 (秒)", "autorefresh": "自动重新整理", "debug": "除错模式"}, "languages": {"zh-TW": "繁體中文", "zh-CN": "简体中文", "en": "English"}, "themes": {"dark": "深色", "light": "浅色", "auto": "自动"}, "status": {"connected": "已连接", "connecting": "连接中...", "disconnected": "已断开连接", "reconnecting": "重新连接中...", "error": "连接错误"}, "notifications": {"feedback_sent": "反馈已发送", "command_executed": "命令已执行", "settings_saved": "设置已保存", "connection_lost": "连接中断", "connection_restored": "连接已恢复"}, "errors": {"connection_failed": "连接失败", "upload_failed": "上传失败", "command_failed": "命令执行失败", "invalid_input": "输入内容无效", "timeout": "请求超时"}, "buttons": {"ok": "确定", "cancel": "❌ 取消", "submit": "✅ 提交反馈", "retry": "重试", "close": "关闭", "upload": "上传", "download": "下载"}, "session": {"timeout": "⏰ 会话已超时，界面将自动关闭", "timeoutWarning": "会话即将超时", "timeoutDescription": "由于长时间无响应，会话已超时。界面将在 3 秒后自动关闭。", "closing": "正在关闭..."}, "timeout": {"enable": "自动关闭", "enableTooltip": "启用后将在指定时间后自动关闭界面", "duration": {"label": "超时时间", "description": "设置自动关闭的时间（30秒 - 2小时）"}, "seconds": "秒", "remaining": "剩余时间", "expired": "⏰ 时间已到，界面将自动关闭", "autoCloseMessage": "界面将在 {seconds} 秒后自动关闭", "settings": {"title": "超时设置", "description": "启用后，界面将在指定时间后自动关闭。倒数计时器会显示在顶部区域。"}}, "dynamic": {"aiSummary": "测试 Web UI 功能\n\n🎯 **功能测试项目：**\n- Web UI 服务器启动和运行\n- WebSocket 实时通讯\n- 反馈提交功能\n- 图片上传和预览\n- 命令执行功能\n- 智能 Ctrl+V 图片粘贴\n- 多语言界面功能\n\n📋 **测试步骤：**\n1. 测试图片上传（拖拽、选择文件、剪贴板）\n2. 在文本框内按 Ctrl+V 测试智能粘贴\n3. 尝试切换语言（繁中/简中/英文）\n4. 测试命令执行功能\n5. 提交反馈和图片\n\n请测试这些功能并提供反馈！", "terminalWelcome": "欢迎使用交互反馈终端\n========================================\n项目目录: {sessionId}\n输入命令后按 Enter 或点击执行按钮\n支持的命令: ls, dir, pwd, cat, type 等\n\n$ "}, "about": {"title": "ℹ️ 关于", "description": "一个强大的 MCP 服务器，为 AI 辅助开发工具提供人在回路的交互反馈功能。支持 Qt GUI 和 Web UI 双界面，并具备图片上传、命令执行、多语言等丰富功能。", "appInfo": "应用程序信息", "version": "版本", "projectLinks": "项目链接", "githubProject": "GitHub 项目", "visitGithub": "访问 GitHub", "contact": "联系与支持", "discordSupport": "Discord 支持", "joinDiscord": "加入 Discord", "contactDescription": "如需技术支持、问题反馈或功能建议，欢迎通过 Discord 社群或 GitHub Issues 与我们联系。", "thanks": "致谢与贡献", "thanksText": "感谢原作者 <PERSON><PERSON><PERSON> (@fabiomlferreira) 创建了原始的 interactive-feedback-mcp 项目。\n\n本增强版本由 Minidoracat 开发和维护，大幅扩展了项目功能，新增了 GUI 界面、图片支持、多语言能力以及许多其他改进功能。\n\n同时感谢 sanshao85 的 mcp-feedback-collector 项目提供的 UI 设计灵感。\n\n开源协作让技术变得更美好！"}, "images": {"settings": {"title": "图片设置", "sizeLimit": "图片大小限制", "sizeLimitOptions": {"unlimited": "无限制", "1mb": "1MB", "3mb": "3MB", "5mb": "5MB"}, "base64Detail": "Base64 兼容模式", "base64DetailHelp": "启用后会在文本中包含完整的 Base64 图片数据，提升与某些 AI 模型的兼容性", "base64Warning": "⚠️ 会增加传输量", "compatibilityHint": "💡 图片无法正确识别？", "enableBase64Hint": "尝试启用 Base64 兼容模式"}, "sizeLimitExceeded": "图片 {filename} 大小为 {size}，超过 {limit} 限制！", "sizeLimitExceededAdvice": "建议使用图片编辑软件压缩后再上传，或调整图片大小限制设置。"}}