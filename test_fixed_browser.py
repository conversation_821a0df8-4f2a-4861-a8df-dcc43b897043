#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修復後的瀏覽器啟動功能
"""

import sys
import os

# 添加 src 目錄到路徑
sys.path.insert(0, 'src')

def test_fixed_browser():
    """測試修復後的瀏覽器啟動功能"""
    print("=== 測試修復後的瀏覽器啟動功能 ===")
    
    try:
        # 設置正確的客戶端 IP
        os.environ['SSH_CLIENT_IP'] = '**************'
        
        from mcp_feedback_enhanced.web.utils.universal_browser import get_universal_launcher
        
        # 獲取啟動器實例
        launcher = get_universal_launcher()
        
        print("\n=== 環境檢測結果 ===")
        env = launcher.environment
        for key, value in env.items():
            print(f"{key}: {value}")
        
        print(f"\n=== 策略列表 ===")
        for i, strategy in enumerate(launcher.strategies):
            print(f"{i+1}. {strategy.__name__}")
        
        print(f"\n=== 配置檢查 ===")
        print(f"單次嘗試模式: {launcher.config.is_single_attempt()}")
        
        print(f"\n=== 開始測試瀏覽器啟動 ===")
        test_url = "http://localhost:8765/test"
        print(f"測試 URL: {test_url}")
        
        # 執行瀏覽器啟動測試
        success = launcher.open_browser(test_url)
        
        print(f"\n=== 測試結果 ===")
        if success:
            print("✅ 瀏覽器啟動報告成功")
        else:
            print("❌ 瀏覽器啟動報告失敗")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_strategies():
    """單獨測試關鍵策略"""
    print("\n=== 單獨測試關鍵策略 ===")
    
    try:
        os.environ['SSH_CLIENT_IP'] = '**************'
        
        from mcp_feedback_enhanced.web.utils.universal_browser import get_universal_launcher
        
        launcher = get_universal_launcher()
        test_url = "http://localhost:8765/test"
        
        # 測試關鍵策略
        key_strategies = [
            launcher._launch_local_browser,
            launcher._launch_system_default,
            launcher._launch_smart_notification,
        ]
        
        for strategy in key_strategies:
            print(f"\n--- 測試策略: {strategy.__name__} ---")
            try:
                result = strategy(test_url)
                if result:
                    print(f"✅ {strategy.__name__} 成功")
                else:
                    print(f"❌ {strategy.__name__} 失敗")
            except Exception as e:
                print(f"💥 {strategy.__name__} 異常: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 單獨測試失敗: {e}")
        return False

if __name__ == "__main__":
    print("開始測試修復後的瀏覽器啟動功能...")
    
    # 測試修復後的功能
    success1 = test_fixed_browser()
    
    # 單獨測試關鍵策略
    success2 = test_individual_strategies()
    
    if success1 and success2:
        print("\n🎉 所有測試完成")
        print("\n💡 如果瀏覽器仍未自動啟動，請檢查：")
        print("1. 是否有瀏覽器安全提示需要確認")
        print("2. 是否需要手動設置 SSH 端口轉發")
        print("3. 檢查防火牆設置")
    else:
        print("\n❌ 部分測試失敗")
    
    sys.exit(0 if (success1 and success2) else 1)
