#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試環境檢測功能
"""

import sys
import os

# 添加 src 目錄到路徑
sys.path.insert(0, 'src')

def test_environment_detection():
    """測試環境檢測"""
    try:
        from mcp_feedback_enhanced.web.utils.universal_browser import get_universal_launcher
        
        print("=== 環境檢測測試 ===")
        
        # 獲取啟動器實例
        launcher = get_universal_launcher()
        
        print("\n=== 當前環境變數 ===")
        env_vars = [
            'SSH_CONNECTION', 'SSH_CLIENT', 'SSH_TTY',
            'VSCODE_INJECTION', 'VSCODE_IPC_HOOK', 'TERM_PROGRAM',
            'CURSOR_USER_DATA_DIR', 'CURSOR_LOGS_DIR',
            'DISPLAY', 'WSL_DISTRO_NAME', 'REMOTE_CONTAINERS'
        ]
        
        for var in env_vars:
            value = os.getenv(var)
            if value:
                print(f"{var}: {value}")
            else:
                print(f"{var}: (未設定)")
        
        print("\n=== 檢測結果 ===")
        env = launcher.environment
        for key, value in env.items():
            print(f"{key}: {value}")
        
        print(f"\n=== 啟動策略 ===")
        for i, strategy in enumerate(launcher.strategies):
            print(f"{i+1}. {strategy.__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_environment_detection()
    sys.exit(0 if success else 1)
